<template>
  <div class="dashboard-container">
    <!-- 顶部搜索栏 -->
    <div class="search-container">
      <div class="search-form">
        <div class="search-input-wrapper">
          <input
            type="text"
            v-model="searchForm.keyword"
            class="search-input"
            placeholder="搜索 门店营业额 前十 门店 营业额"
          />
        </div>
        <div class="search-buttons">
          <button type="button" class="btn-icon" @click="handleClose">
            <i class="close-icon"></i>
          </button>
          <button type="button" class="btn-icon" @click="handleSearch">
            <i class="search-icon"></i>
          </button>
          <button type="button" class="btn-icon" @click="handleMinus">
            <i class="minus-icon"></i>
          </button>
          <button type="button" class="btn-icon" @click="handleFilter" ref="filterButton">
            <i class="filter-icon"></i>
          </button>
        </div>

        <!-- 筛选弹窗 -->
        <div v-if="showFilterPopup" class="filter-popup" :style="filterPopupStyle">
          <div class="popup-header">
            <span>数据</span>
            <button class="popup-close" @click="closeFilterPopup">×</button>
          </div>
          <div class="popup-search">
            <input type="text" class="search-input" placeholder="搜索" v-model="filterSearchQuery">
            <i class="search-icon"></i>
          </div>
          <div class="popup-tabs">
            <div class="tab-item" :class="{ active: activeTab === '维度' }" @click="activeTab = '维度'">维度</div>
            <div class="tab-item" :class="{ active: activeTab === '时间维度' }" @click="activeTab = '时间维度'">时间维度</div>
            <div class="tab-item" :class="{ active: activeTab === '指标' }" @click="activeTab = '指标'">指标</div>
            <div class="tab-item" :class="{ active: activeTab === '分析' }" @click="activeTab = '分析'">分析</div>
          </div>
          <div class="popup-content">
            <div v-if="activeTab === '维度'" class="tab-content">
              <div class="filter-item" @click="selectFilter('门店')">
                <span>门店</span>
                <i class="arrow-icon">^</i>
              </div>
              <div class="filter-item" @click="selectFilter('品牌')">
                <span>品牌</span>
                <i class="arrow-icon">^</i>
              </div>
              <div class="filter-item" @click="selectFilter('等')">
                <span>等</span>
                <i class="arrow-icon">^</i>
              </div>
              <div class="filter-item" @click="selectFilter('综合分析')">
                <span>综合分析</span>
                <i class="arrow-icon">^</i>
              </div>
              <div class="filter-item" @click="selectFilter('门店营业额')">
                <span>门店营业额</span>
                <i class="arrow-icon">^</i>
              </div>
              <div class="filter-item" @click="selectFilter('数据分析')">
                <span>数据分析</span>
                <i class="arrow-icon">^</i>
              </div>
            </div>
            <div v-if="activeTab === '时间维度'" class="tab-content">
              <div class="filter-item">
                <span>日期</span>
                <i class="arrow-icon down">v</i>
              </div>
              <div class="time-units-row">
                <span class="time-unit" @click="selectFilter('日')">日</span>
                <span class="time-unit" @click="selectFilter('周')">周</span>
                <span class="time-unit" @click="selectFilter('月')">月</span>
                <span class="time-unit" @click="selectFilter('季')">季</span>
                <span class="time-unit" @click="selectFilter('年')">年</span>
              </div>
              <div class="time-item">当日</div>
              <div class="time-item">数天</div>
              <div class="time-item">数十天</div>
              <div class="time-item">数月</div>
              <div class="time-item">2月1日至16日</div>
              <div class="time-item">2月1日至今</div>
            </div>
            <div v-if="activeTab === '指标'" class="tab-content">
              <div class="filter-item" @click="selectFilter('出店')">出店</div>
              <div class="filter-item" @click="selectFilter('进店顾客')">进店顾客</div>
              <div class="filter-item" @click="selectFilter('客单')">客单</div>
              <div class="filter-item" @click="selectFilter('分析')">分析</div>
              <div class="filter-item" @click="selectFilter('利润')">利润</div>
              <div class="filter-item" @click="selectFilter('销售额')">销售额</div>
              <div class="filter-item" @click="selectFilter('进货数量')">进货数量</div>
              <div class="filter-item" @click="selectFilter('退货数量')">退货数量</div>
              <div class="filter-item" @click="selectFilter('总价值')">总价值</div>
              <div class="filter-item" @click="selectFilter('公司利润率')">公司利润率</div>
              <div class="filter-item" @click="selectFilter('客户数量')">客户数量</div>
              <div class="filter-item" @click="selectFilter('今日利润')">今日利润</div>
              <div class="filter-item" @click="selectFilter('全店成本率')">全店成本率</div>
            </div>
            <div v-if="activeTab === '分析'" class="tab-content">
              <div class="filter-item" @click="selectFilter('出店')">出店</div>
              <div class="filter-item" @click="selectFilter('增长')">增长</div>
              <div class="filter-item" @click="selectFilter('开店')">开店</div>
              <div class="filter-item" @click="selectFilter('同比')">同比</div>
              <div class="filter-item" @click="selectFilter('成交率')">成交率</div>
              <div class="filter-item" @click="selectFilter('分析')">分析</div>
              <div class="filter-item" @click="selectFilter('同比')">同比</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 顶部：门店营业额前十的 + 智能助手 -->
    <div class="top-section">
      <div class="chart-card main-chart">
        <div class="chart-header">
          <div class="chart-title">
            <i class="chart-icon"></i>
            <span>门店营业额前十的</span>
            <i class="help-icon">?</i>
          </div>
          <div class="chart-meta">
            <span class="chart-date">2024-01-01 至 12-31</span>
            <span class="chart-type">月报</span>
            <span class="chart-source">按营业额排序</span>
          </div>
          <div class="chart-actions">
            <i class="action-icon refresh" @click="handleRefresh"></i>
            <i class="action-icon download" @click="handleDownload"></i>
            <i class="action-icon more" ref="moreButton1" @click="handleMoreClick($event)"></i>
            <i class="action-icon settings" @click="handleSettings"></i>
            <span class="chart-status">数据加载中</span>
            <i class="action-icon close" @click="handleClose"></i>
          </div>
        </div>
        <div class="chart-content">
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-color blue"></span>
              <span>营业额/万元</span>
            </div>
            <div class="legend-item">
              <span class="legend-color yellow"></span>
              <span>利润/万元</span>
            </div>
            <div class="legend-item">
              <span class="legend-color line"></span>
              <span>营业额同比增长率</span>
            </div>
          </div>
          <div class="chart-wrapper">
            <div ref="storeRevenueChart" class="chart" style="height: 300px;"></div>
          </div>
        </div>
      </div>

      <!-- 智能助手面板 -->
      <div class="assistant-panel">
        <div class="panel-header">
          <span>智能助手</span>
          <div class="header-actions">
            <button class="send-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="white"/>
              </svg>
            </button>
            <i class="panel-close">×</i>
          </div>
        </div>
        <div class="panel-content">
          <div class="chat-messages">
            <div v-for="(message, index) in chatMessages" :key="index"
                 :class="['message-item', message.type === 'user' ? 'user-message' : 'assistant-message']">
              <div v-if="message.type === 'assistant'" class="message-avatar">
                <div class="avatar-circle">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.5 10.84L11.91 12.25L15.83 8.33L17.5 10H19V9H21ZM1 9H3V7H1V9ZM13 12L10.5 9.5L9.09 10.91L13 14.82L13 12ZM5 13L6.5 14.5L3.91 17.09L2.5 15.67L5 13ZM12 15C10.9 15 10 15.9 10 17C10 18.1 10.9 19 12 19C13.1 19 14 18.1 14 17C14 15.9 13.1 15 12 15Z" fill="#1890ff"/>
                  </svg>
                </div>
              </div>
              <div class="message-content">
                <div class="message-text" v-html="message.text.replace(/\n/g, '<br>')"></div>
                <div class="message-time">{{ message.time }}</div>
              </div>
            </div>
            <div v-if="chatMessages.length === 1" v-for="suggestion in suggestions" :key="suggestion.text"
                 class="suggestion-item" @click="handleSuggestionClick(suggestion)">
              <div class="suggestion-icon">{{ suggestion.icon }}</div>
              <div class="suggestion-text">{{ suggestion.text }}</div>
            </div>
          </div>
          <div class="input-area">
            <div class="input-wrapper">
              <input type="text" v-model="chatInput" @keyup.enter="sendChatMessage"
                     placeholder="请输入您的问题..." class="chat-input">
              <button class="input-send-btn" @click="sendChatMessage">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="#1890ff"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中间：营业额同比 单独一行 -->
    <div class="middle-section">
      <div class="value-card">
        <div class="value-header">
          <div class="value-title">
            <i class="chart-icon"></i>
            <span>营业额同比</span>
            <i class="help-icon">?</i>
          </div>
          <div class="value-meta">
            <span class="value-date">2024-01-01 至 12-31</span>
            <span class="value-type">月报</span>
          </div>
          <div class="value-actions">
            <i class="action-icon refresh" @click="handleRefresh"></i>
            <i class="action-icon more" ref="moreButton2" @click="handleMoreClick($event)"></i>
            <i class="action-icon settings" @click="handleSettings"></i>
          </div>
        </div>
        <div class="value-content">
          <div class="value-main">
            <span class="value-label">营业额(总) / 元</span>
            <div class="value-number">165.32<span class="value-unit">亿</span></div>
            <div class="value-change">
              <span class="change-text">同比上期</span>
              <span class="change-value positive">+4.73%(+7.43亿)</span>
              <i class="change-arrow up"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部：品牌门店营业额前十的 + 智能助手 -->
    <div class="bottom-section">
      <div class="chart-card main-chart">
        <div class="chart-header">
          <div class="chart-title">
            <i class="chart-icon"></i>
            <span>品牌门店营业额前十的</span>
            <i class="help-icon">?</i>
          </div>
          <div class="chart-meta">
            <span class="chart-date">2024-01-01 至 12-31</span>
            <span class="chart-type">月报</span>
            <span class="chart-source">按营业额排序</span>
          </div>
          <div class="chart-actions">
            <i class="action-icon refresh" @click="handleRefresh"></i>
            <i class="action-icon download" @click="handleDownload"></i>
            <i class="action-icon more" ref="moreButton3" @click="handleMoreClick($event)"></i>
            <i class="action-icon settings" @click="handleSettings"></i>
            <span class="chart-status">数据加载中</span>
            <i class="action-icon close" @click="handleClose"></i>
          </div>
        </div>
        <div class="chart-content">
          <div class="chart-legend">
            <div class="legend-item">
              <span class="legend-color blue"></span>
              <span>营业额/万元</span>
            </div>
            <div class="legend-item">
              <span class="legend-color yellow"></span>
              <span>利润/万元</span>
            </div>
            <div class="legend-item">
              <span class="legend-color line"></span>
              <span>营业额同比增长率</span>
            </div>
          </div>
          <div class="chart-wrapper">
            <div ref="cloudRevenueChart" class="chart" style="height: 300px;"></div>
          </div>
        </div>
      </div>

      <!-- 智能助手面板 -->
      <div class="assistant-panel">
        <div class="panel-header">
          <span>智能助手</span>
          <div class="header-actions">
            <button class="send-btn">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="white"/>
              </svg>
            </button>
            <i class="panel-close">×</i>
          </div>
        </div>
        <div class="panel-content">
          <div class="chat-messages">
            <div v-for="(message, index) in chatMessages" :key="'bottom-' + index"
                 :class="['message-item', message.type === 'user' ? 'user-message' : 'assistant-message']">
              <div v-if="message.type === 'assistant'" class="message-avatar">
                <div class="avatar-circle">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.5 10.84L11.91 12.25L15.83 8.33L17.5 10H19V9H21ZM1 9H3V7H1V9ZM13 12L10.5 9.5L9.09 10.91L13 14.82L13 12ZM5 13L6.5 14.5L3.91 17.09L2.5 15.67L5 13ZM12 15C10.9 15 10 15.9 10 17C10 18.1 10.9 19 12 19C13.1 19 14 18.1 14 17C14 15.9 13.1 15 12 15Z" fill="#1890ff"/>
                  </svg>
                </div>
              </div>
              <div class="message-content">
                <div class="message-text" v-html="message.text.replace(/\n/g, '<br>')"></div>
                <div class="message-time">{{ message.time }}</div>
              </div>
            </div>
            <div v-if="chatMessages.length === 1" v-for="suggestion in suggestions" :key="'bottom-' + suggestion.text"
                 class="suggestion-item" @click="handleSuggestionClick(suggestion)">
              <div class="suggestion-icon">{{ suggestion.icon }}</div>
              <div class="suggestion-text">{{ suggestion.text }}</div>
            </div>
          </div>
          <div class="input-area">
            <div class="input-wrapper">
              <input type="text" v-model="chatInput" @keyup.enter="sendChatMessage"
                     placeholder="请输入您的问题..." class="chat-input">
              <button class="input-send-btn" @click="sendChatMessage">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="#1890ff"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 更多操作弹窗 -->
    <div v-if="showMorePopup" class="more-popup" :style="morePopupStyle" @click.stop>
      <div class="more-popup-content">
        <div class="more-action-item" @click.stop="handleCardReminder">卡片提醒</div>
        <div class="more-action-item" @click.stop="handleShareCard">分享卡片</div>
        <div class="more-action-item" @click="handleSaveCard">保存卡片</div>
        <div class="more-action-item" @click.stop="handleUploadCSV">上传CSV</div>
        <div class="more-action-item" @click="handleDownloadPNG">下载PNG</div>
      </div>
    </div>

    <!-- 分享卡片弹窗 -->
    <div v-if="showSharePopup" class="share-popup-overlay" @click="closeSharePopup">
      <div class="share-popup" @click.stop>
        <div class="share-popup-header">
          <span class="share-popup-title">分享链接</span>
          <button class="share-popup-close" @click="closeSharePopup">×</button>
        </div>
        <div class="share-popup-content">
          <div class="share-description">
            分享分析结果，让更多的人看到你的洞察
          </div>
          <div class="share-option">
            <div class="share-option-label">
              <span>代码嵌入功能</span>
            </div>
            <div class="share-toggle">
              <input type="checkbox" id="embedToggle" v-model="embedEnabled" class="toggle-input">
              <label for="embedToggle" class="toggle-label"></label>
            </div>
          </div>
          <div class="share-link-section">
            <input
              type="text"
              class="share-link-input"
              :value="shareLink"
              readonly
              placeholder="https://dwz.cn/jzwMdMh"
            >
            <button class="copy-link-btn" @click="copyShareLink">复制链接</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 卡片提醒弹窗 -->
    <div v-if="showReminderPopup" class="reminder-popup-overlay" @click="closeReminderPopup">
      <div class="reminder-popup" @click.stop>
        <div class="reminder-popup-header">
          <span class="reminder-popup-title">卡片提醒设置</span>
          <button class="reminder-popup-close" @click="closeReminderPopup">×</button>
        </div>
        <div class="reminder-popup-content">
          <div class="reminder-form-item">
            <label class="reminder-label">提醒卡片</label>
            <select class="reminder-select" v-model="reminderForm.cardName">
              <option value="">请选择卡片</option>
              <option value="门店营业额前十">门店营业额前十</option>
              <option value="云营业额前十">云营业额前十</option>
            </select>
          </div>

          <div class="reminder-form-item">
            <label class="reminder-label">提醒邮箱地址</label>
            <input
              type="email"
              class="reminder-input"
              v-model="reminderForm.email"
              placeholder="请输入邮箱地址"
            >
          </div>

          <div class="reminder-form-item">
            <label class="reminder-label">数据变化</label>
            <div class="reminder-change-section">
              <select class="reminder-select-small" v-model="reminderForm.changeType">
                <option value="同比增减幅">同比增减幅/元</option>
                <option value="环比增减幅">环比增减幅/元</option>
              </select>
              <select class="reminder-select-small" v-model="reminderForm.timePeriod">
                <option value="天数">天数(天)</option>
                <option value="周数">周数(周)</option>
                <option value="月数">月数(月)</option>
              </select>
            </div>
          </div>

          <div class="reminder-form-item">
            <div class="reminder-threshold-section">
              <input
                type="number"
                class="reminder-number-input"
                v-model="reminderForm.threshold"
                placeholder="0"
              >
              <span class="reminder-unit">元</span>
              <div class="reminder-checkbox-section">
                <input
                  type="checkbox"
                  id="contentChange"
                  v-model="reminderForm.contentChange"
                  class="reminder-checkbox"
                >
                <label for="contentChange" class="reminder-checkbox-label">内容变化提醒</label>
              </div>
            </div>
          </div>

          <div class="reminder-description">
            当选择指标比上月数据变化超过设定阈值时，发送邮件提醒
          </div>

          <div class="reminder-form-item">
            <label class="reminder-label">提醒方式</label>
            <div class="reminder-method-section">
              <div class="reminder-radio-item">
                <input
                  type="radio"
                  id="emailMethod"
                  value="email"
                  v-model="reminderForm.method"
                  class="reminder-radio"
                >
                <label for="emailMethod" class="reminder-radio-label">邮件提醒</label>
              </div>
              <div class="reminder-radio-item">
                <input
                  type="radio"
                  id="smsMethod"
                  value="sms"
                  v-model="reminderForm.method"
                  class="reminder-radio"
                >
                <label for="smsMethod" class="reminder-radio-label">短信提醒</label>
              </div>
            </div>
          </div>
        </div>

        <div class="reminder-popup-footer">
          <button class="reminder-cancel-btn" @click="closeReminderPopup">取消</button>
          <button class="reminder-confirm-btn" @click="confirmReminder">确定，设置提醒人</button>
        </div>
      </div>
    </div>

    <!-- 上传CSV弹窗 -->
    <div v-if="showUploadPopup" class="upload-popup-overlay" @click="closeUploadPopup">
      <div class="upload-popup" @click.stop>
        <div class="upload-popup-header">
          <span class="upload-popup-title">加入报告</span>
          <button class="upload-popup-close" @click="closeUploadPopup">×</button>
        </div>
        <div class="upload-popup-content">
          <div class="upload-form-item">
            <label class="upload-label">报告名称</label>
            <input
              type="text"
              class="upload-input"
              v-model="uploadForm.reportName"
              placeholder="请输入报告名称"
            >
          </div>

          <div class="upload-form-item">
            <label class="upload-label">描述信息</label>
            <textarea
              class="upload-textarea"
              v-model="uploadForm.description"
              placeholder="请输入描述信息"
              rows="3"
            ></textarea>
          </div>

          <div class="upload-form-item">
            <label class="upload-label">上传文件</label>
            <div class="upload-file-section">
              <input
                type="file"
                accept=".csv"
                @change="handleFileSelect"
                class="upload-file-input"
                id="csvFileInput"
              >
              <label for="csvFileInput" class="upload-file-button">
                选择文件
              </label>
              <span class="upload-file-name" v-if="uploadForm.file">
                {{ uploadForm.file.name }}
              </span>
              <span class="upload-file-placeholder" v-else>
                请选择CSV文件
              </span>
            </div>
          </div>

          <div class="upload-tips">
            <div class="upload-tips-title">上传说明：</div>
            <div class="upload-tips-content">
              • 支持CSV格式文件<br>
              • 文件大小不超过10MB<br>
              • 请确保数据格式正确
            </div>
          </div>
        </div>

        <div class="upload-popup-footer">
          <button class="upload-cancel-btn" @click="closeUploadPopup">取消</button>
          <button class="upload-confirm-btn" @click="confirmUpload">确定</button>
        </div>
      </div>
    </div>

    <!-- 设置弹窗 -->
    <div v-if="showSettingsPopup" class="settings-popup" :style="settingsPopupStyle" @click.stop>
      <div class="settings-popup-content">
        <div class="chart-types-grid">
          <div class="chart-type-item" @click="selectChartIcon('bar')">
            <div class="chart-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="12" width="4" height="9" fill="#1890ff"/>
                <rect x="10" y="8" width="4" height="13" fill="#1890ff"/>
                <rect x="17" y="4" width="4" height="17" fill="#1890ff"/>
              </svg>
            </div>
            <span class="chart-label">柱状图</span>
          </div>
          <div class="chart-type-item" @click="selectChartIcon('line')">
            <div class="chart-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M3 17L9 11L13 15L21 7" stroke="#1890ff" stroke-width="2" fill="none"/>
                <circle cx="3" cy="17" r="2" fill="#1890ff"/>
                <circle cx="9" cy="11" r="2" fill="#1890ff"/>
                <circle cx="13" cy="15" r="2" fill="#1890ff"/>
                <circle cx="21" cy="7" r="2" fill="#1890ff"/>
              </svg>
            </div>
            <span class="chart-label">折线图</span>
          </div>
          <div class="chart-type-item" @click="selectChartIcon('pie')">
            <div class="chart-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M12 2V12L20.5 7.5C19.5 4.5 16 2 12 2Z" fill="#1890ff"/>
                <path d="M12 12L20.5 16.5C19.5 19.5 16 22 12 22C7 22 3 18 3 12C3 7 7 3 12 3V12Z" fill="#52c41a"/>
              </svg>
            </div>
            <span class="chart-label">饼图</span>
          </div>
          <div class="chart-type-item" @click="selectChartIcon('area')">
            <div class="chart-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M3 17L9 11L13 15L21 7V21H3V17Z" fill="#1890ff" opacity="0.3"/>
                <path d="M3 17L9 11L13 15L21 7" stroke="#1890ff" stroke-width="2" fill="none"/>
              </svg>
            </div>
            <span class="chart-label">面积图</span>
          </div>
          <div class="chart-type-item" @click="selectChartIcon('scatter')">
            <div class="chart-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <circle cx="5" cy="18" r="2" fill="#1890ff"/>
                <circle cx="9" cy="12" r="2" fill="#1890ff"/>
                <circle cx="13" cy="16" r="2" fill="#1890ff"/>
                <circle cx="17" cy="8" r="2" fill="#1890ff"/>
                <circle cx="21" cy="14" r="2" fill="#1890ff"/>
              </svg>
            </div>
            <span class="chart-label">散点图</span>
          </div>
          <div class="chart-type-item" @click="selectChartIcon('radar')">
            <div class="chart-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <polygon points="12,2 20,8 20,16 12,22 4,16 4,8" stroke="#1890ff" stroke-width="1" fill="none"/>
                <polygon points="12,6 16,9 16,15 12,18 8,15 8,9" stroke="#1890ff" stroke-width="1" fill="#1890ff" opacity="0.3"/>
              </svg>
            </div>
            <span class="chart-label">雷达图</span>
          </div>
          <div class="chart-type-item" @click="selectChartIcon('gauge')">
            <div class="chart-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="#1890ff" stroke-width="2" fill="none"/>
                <path d="M12 12L16 8" stroke="#1890ff" stroke-width="2"/>
                <circle cx="12" cy="12" r="2" fill="#1890ff"/>
              </svg>
            </div>
            <span class="chart-label">仪表盘</span>
          </div>
          <div class="chart-type-item" @click="selectChartIcon('funnel')">
            <div class="chart-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M6 4H18L16 8H8L6 4Z" fill="#1890ff"/>
                <path d="M8 8H16L14 12H10L8 8Z" fill="#52c41a"/>
                <path d="M10 12H14L13 16H11L10 12Z" fill="#faad14"/>
                <path d="M11 16H13V20H11V16Z" fill="#f5222d"/>
              </svg>
            </div>
            <span class="chart-label">漏斗图</span>
          </div>
          <div class="chart-type-item" @click="selectChartIcon('heatmap')">
            <div class="chart-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="4" height="4" fill="#1890ff"/>
                <rect x="8" y="3" width="4" height="4" fill="#52c41a"/>
                <rect x="13" y="3" width="4" height="4" fill="#faad14"/>
                <rect x="18" y="3" width="3" height="4" fill="#f5222d"/>
                <rect x="3" y="8" width="4" height="4" fill="#52c41a"/>
                <rect x="8" y="8" width="4" height="4" fill="#faad14"/>
                <rect x="13" y="8" width="4" height="4" fill="#f5222d"/>
                <rect x="18" y="8" width="3" height="4" fill="#1890ff"/>
              </svg>
            </div>
            <span class="chart-label">热力图</span>
          </div>
          <div class="chart-type-item" @click="selectChartIcon('treemap')">
            <div class="chart-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <rect x="2" y="2" width="10" height="8" fill="#1890ff" stroke="#fff" stroke-width="1"/>
                <rect x="13" y="2" width="9" height="5" fill="#52c41a" stroke="#fff" stroke-width="1"/>
                <rect x="13" y="8" width="9" height="3" fill="#faad14" stroke="#fff" stroke-width="1"/>
                <rect x="2" y="11" width="6" height="11" fill="#f5222d" stroke="#fff" stroke-width="1"/>
                <rect x="9" y="11" width="13" height="11" fill="#722ed1" stroke="#fff" stroke-width="1"/>
              </svg>
            </div>
            <span class="chart-label">矩形树图</span>
          </div>
          <div class="chart-type-item" @click="selectChartIcon('sunburst')">
            <div class="chart-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="10" fill="none" stroke="#1890ff" stroke-width="2"/>
                <circle cx="12" cy="12" r="6" fill="none" stroke="#52c41a" stroke-width="2"/>
                <circle cx="12" cy="12" r="3" fill="#faad14"/>
                <path d="M12 2L14 6L12 6L10 6L12 2Z" fill="#1890ff"/>
                <path d="M22 12L18 14L18 12L18 10L22 12Z" fill="#1890ff"/>
              </svg>
            </div>
            <span class="chart-label">旭日图</span>
          </div>
          <div class="chart-type-item" @click="selectChartIcon('sankey')">
            <div class="chart-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M2 6C2 6 8 6 12 10C16 14 22 14 22 14" stroke="#1890ff" stroke-width="3" fill="none"/>
                <path d="M2 12C2 12 8 12 12 12C16 12 22 12 22 12" stroke="#52c41a" stroke-width="3" fill="none"/>
                <path d="M2 18C2 18 8 18 12 14C16 10 22 10 22 10" stroke="#faad14" stroke-width="3" fill="none"/>
              </svg>
            </div>
            <span class="chart-label">桑基图</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: "DataSearch",
  data() {
    return {
      // 搜索表单数据
      searchForm: {
        keyword: '门店 营业额 前十 门店 营业额',
        dataType: 'store',
        store: 'all',
        time: '2024'
      },
      // 筛选弹窗相关
      showFilterPopup: false,
      filterPopupStyle: {},
      filterSearchQuery: '',
      activeTab: '维度',
      // 更多操作弹窗
      showMorePopup: false,
      morePopupStyle: {},
      // 分享弹窗相关
      showSharePopup: false,
      embedEnabled: true,
      shareLink: 'https://dwz.cn/jzwMdMh',

      // 卡片提醒弹窗
      showReminderPopup: false,
      reminderForm: {
        cardName: '',
        email: '',
        changeType: '同比增减幅',
        timePeriod: '天数',
        threshold: 0,
        contentChange: false,
        method: 'email'
      },
      // 上传CSV弹窗
      showUploadPopup: false,
      uploadForm: {
        reportName: '',
        description: '',
        file: null
      },
      // 图表选择弹窗
      showChartSelector: false,
      selectedChartType: 'bar',
      // 设置弹窗
      showSettingsPopup: false,
      settingsPopupStyle: {},
      // 图表实例
      storeRevenueChart: null,
      cloudRevenueChart: null,
      // 刷新间隔
      refreshInterval: '0',
      // 智能助手相关
      chatMessages: [
        {
          type: 'assistant',
          text: '根据当前数据表现？',
          time: '刚刚'
        }
      ],
      chatInput: '',
      suggestions: [
        { icon: '💡', text: '深圳门店营业额最高，有什么成功经验可以分享？' },
        { icon: '📊', text: '如何提升其他门店的营业额？' },
        { icon: '🎯', text: '引用数据分析' }
      ],
      // 数据加载状态
      isLoading: false,
      // 搜索历史
      searchHistory: [
        '门店 营业额 前十',
        '品牌 销售额 排行',
        '区域 业绩 对比'
      ],
      // 门店营业额数据
      storeRevenueData: {
        categories: ['深圳', '广州', '北京', '上海', '杭州', '南京', '成都', '重庆', '武汉', '西安', '天津'],
        revenue: [21340, 16200, 14100, 8100, 7610, 6200, 5310, 4860, 4340, 3460, 3140],
        profit: [22410, 18940, 14200, 12400, 7600, 6420, 5400, 4740, 4360, 3740, 3140],
        growthRate: [11.39, 9.04, 8.31, 7.60, 5.37, 5.04, 4.74, 4.34, 4.17, 3.86, 3.70]
      },
      // 云营业额数据
      cloudRevenueData: {
        categories: ['华南大区_深圳', '华南大区_广州', '华东大区_上海', '华东大区_杭州', '华北大区_北京', '华中大区_武汉', '西南大区_成都', '西南大区_重庆', '西北大区_西安', '华北大区_天津', '华东大区_南京'],
        revenue: [6000000, 5800000, 4100000, 4100000, 3400000, 2600000, 2400000, 2100000, 2000000, 1900000, 1800000],
        profit: [5800000, 5600000, 4100000, 4100000, 3200000, 2400000, 2200000, 2000000, 1900000, 1800000, 1700000],
        growthRate: [4.70, -0.2, -6.3, -6.3, 1.9, 2.6, 2.7, 2.1, 2.0, 1.9, 1.8]
      }
    };
  },
  mounted() {
    this.initCharts();
    // 添加点击外部关闭弹窗的事件监听
    document.addEventListener('click', this.handleClickOutside);
    // 添加键盘快捷键
    document.addEventListener('keydown', this.handleKeydown);
    // 启动实时数据更新
    this.startRealTimeUpdate();
    // 显示快捷键提示
    this.showKeyboardShortcuts();
  },
  beforeDestroy() {
    if (this.storeRevenueChart) {
      this.storeRevenueChart.dispose();
    }
    if (this.cloudRevenueChart) {
      this.cloudRevenueChart.dispose();
    }
    // 移除事件监听
    document.removeEventListener('click', this.handleClickOutside);
    document.removeEventListener('keydown', this.handleKeydown);
  },
  methods: {
    /** 初始化图表 */
    initCharts() {
      this.$nextTick(() => {
        this.initStoreRevenueChart();
        this.initCloudRevenueChart();
      });
    },

    /** 初始化门店营业额图表 */
    initStoreRevenueChart() {
      this.storeRevenueChart = echarts.init(this.$refs.storeRevenueChart);

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          show: false
        },
        xAxis: [
          {
            type: 'category',
            data: this.storeRevenueData.categories,
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              rotate: 45,
              fontSize: 10
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '营业额(万元)',
            position: 'left',
            axisLabel: {
              formatter: function(value) {
                if (value >= 10000) {
                  return (value / 10000).toFixed(1) + '万';
                }
                return value;
              }
            }
          },
          {
            type: 'value',
            name: '营业额同比增长率',
            position: 'right',
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        series: [
          {
            name: '营业额/万元',
            type: 'bar',
            data: this.storeRevenueData.revenue,
            itemStyle: {
              color: '#5B8FF9'
            },
            barWidth: '20%',
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return (params.value / 10000).toFixed(1) + '万';
              },
              fontSize: 10
            }
          },
          {
            name: '利润/万元',
            type: 'bar',
            data: this.storeRevenueData.profit,
            itemStyle: {
              color: '#FFD666'
            },
            barWidth: '20%',
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return (params.value / 10000).toFixed(1) + '万';
              },
              fontSize: 10
            }
          },
          {
            name: '营业额同比增长率',
            type: 'line',
            yAxisIndex: 1,
            data: this.storeRevenueData.growthRate,
            itemStyle: {
              color: '#FF6B6B'
            },
            lineStyle: {
              width: 2
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}%',
              fontSize: 10
            }
          }
        ]
      };

      this.storeRevenueChart.setOption(option);

      // 添加点击事件
      this.storeRevenueChart.on('click', (params) => {
        this.handleChartClick(params);
      });
    },

    /** 初始化云营业额图表 */
    initCloudRevenueChart() {
      this.cloudRevenueChart = echarts.init(this.$refs.cloudRevenueChart);

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        legend: {
          show: false
        },
        xAxis: [
          {
            type: 'category',
            data: this.cloudRevenueData.categories,
            axisPointer: {
              type: 'shadow'
            },
            axisLabel: {
              rotate: 45,
              fontSize: 10
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '营业额(万元)',
            position: 'left',
            axisLabel: {
              formatter: function(value) {
                return (value / 10000).toFixed(0) + '万';
              }
            }
          },
          {
            type: 'value',
            name: '营业额同比增长率',
            position: 'right',
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        series: [
          {
            name: '营业额/万元',
            type: 'bar',
            data: this.cloudRevenueData.revenue,
            itemStyle: {
              color: '#5B8FF9'
            },
            barWidth: '20%',
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return (params.value / 10000).toFixed(0) + '万';
              },
              fontSize: 10
            }
          },
          {
            name: '利润/万元',
            type: 'bar',
            data: this.cloudRevenueData.profit,
            itemStyle: {
              color: '#FFD666'
            },
            barWidth: '20%',
            label: {
              show: true,
              position: 'top',
              formatter: function(params) {
                return (params.value / 10000).toFixed(0) + '万';
              },
              fontSize: 10
            }
          },
          {
            name: '营业额同比增长率',
            type: 'line',
            yAxisIndex: 1,
            data: this.cloudRevenueData.growthRate,
            itemStyle: {
              color: '#FF6B6B'
            },
            lineStyle: {
              width: 2
            },
            label: {
              show: true,
              position: 'top',
              formatter: '{c}%',
              fontSize: 10
            }
          }
        ]
      };

      this.cloudRevenueChart.setOption(option);

      // 添加点击事件
      this.cloudRevenueChart.on('click', (params) => {
        this.handleChartClick(params);
      });
    },

    // 搜索栏图标按钮方法
    handleClose() {
      console.log('关闭搜索');
      this.$message.info('关闭搜索');
    },

    handleSearch() {
      this.handleSearchEnhanced();
    },

    handleMinus() {
      console.log('减号操作');
      this.$message.info('减号功能开发中...');
    },

    handleFilter() {
      console.log('筛选功能');
      this.showFilterPopup = !this.showFilterPopup;

      if (this.showFilterPopup) {
        this.$nextTick(() => {
          this.setFilterPopupPosition();
        });
      }
    },

    // 设置弹窗位置
    setFilterPopupPosition() {
      const filterButton = this.$refs.filterButton;
      if (filterButton) {
        const rect = filterButton.getBoundingClientRect();
        this.filterPopupStyle = {
          position: 'fixed',
          top: rect.bottom + 5 + 'px',
          right: (window.innerWidth - rect.right) + 'px',
          zIndex: 1000
        };
      }
    },

    // 关闭筛选弹窗
    closeFilterPopup() {
      this.showFilterPopup = false;
    },

    // 选择筛选项
    selectFilter(item) {
      console.log('选择筛选项:', item);
      this.$message.success(`已选择: ${item}`);
      this.closeFilterPopup();
    },

    // 点击外部关闭弹窗
    handleClickOutside(event) {
      // 处理筛选弹窗
      if (this.showFilterPopup) {
        const filterButton = this.$refs.filterButton;
        const popup = event.target.closest('.filter-popup');

        // 如果点击的不是筛选按钮也不是弹窗内部，则关闭弹窗
        if (!filterButton?.contains(event.target) && !popup) {
          this.closeFilterPopup();
        }
      }

      // 处理更多操作弹窗
      if (this.showMorePopup) {
        const moreButtons = [
          this.$refs.moreButton1,
          this.$refs.moreButton2,
          this.$refs.moreButton3
        ];
        const morePopup = event.target.closest('.more-popup');

        // 检查是否点击了任何更多按钮
        const clickedMoreButton = moreButtons.some(button =>
          button && button.contains(event.target)
        );

        // 如果点击的不是更多按钮也不是弹窗内部，则关闭弹窗
        if (!clickedMoreButton && !morePopup) {
          this.closeMorePopup();
        }
      }

      // 处理分享弹窗
      if (this.showSharePopup) {
        const sharePopup = event.target.closest('.share-popup');
        // 如果点击的不是弹窗内部，则关闭弹窗
        if (!sharePopup) {
          this.closeSharePopup();
        }
      }

      // 处理卡片提醒弹窗
      if (this.showReminderPopup) {
        const reminderPopup = event.target.closest('.reminder-popup');
        // 如果点击的不是弹窗内部，则关闭弹窗
        if (!reminderPopup) {
          this.closeReminderPopup();
        }
      }

      // 处理上传CSV弹窗
      if (this.showUploadPopup) {
        const uploadPopup = event.target.closest('.upload-popup');
        // 如果点击的不是弹窗内部，则关闭弹窗
        if (!uploadPopup) {
          this.closeUploadPopup();
        }
      }

      // 处理图表选择器弹窗
      if (this.showChartSelector) {
        const chartSelector = event.target.closest('.chart-selector');
        // 如果点击的不是弹窗内部，则关闭弹窗
        if (!chartSelector) {
          this.closeChartSelector();
        }
      }

      // 处理设置弹窗
      if (this.showSettingsPopup) {
        const settingsPopup = event.target.closest('.settings-popup');
        const settingsButtons = document.querySelectorAll('.action-icon.settings');

        // 检查是否点击了任何设置按钮
        const clickedSettingsButton = Array.from(settingsButtons).some(button =>
          button && button.contains(event.target)
        );

        // 如果点击的不是设置按钮也不是弹窗内部，则关闭弹窗
        if (!clickedSettingsButton && !settingsPopup) {
          this.closeSettingsPopup();
        }
      }
    },

    // 更多操作相关方法
    handleMoreClick(event) {
      this.showMorePopup = true;

      this.$nextTick(() => {
        this.setMorePopupPosition(event.target);
      });
    },

    // 设置更多弹窗位置
    setMorePopupPosition(buttonElement) {
      if (buttonElement) {
        const rect = buttonElement.getBoundingClientRect();
        this.morePopupStyle = {
          position: 'fixed',
          top: rect.bottom + 5 + 'px',
          left: rect.left - 60 + 'px', // 向左偏移一些，让弹窗居中对齐按钮
          zIndex: 2000
        };
      }
    },

    closeMorePopup() {
      this.showMorePopup = false;
    },

    handleCardReminder() {
      this.showReminderPopup = true;
      this.closeMorePopup();
    },

    handleShareCard() {
      this.showSharePopup = true;
      this.closeMorePopup();
      this.$message.success('分享弹窗已打开');
    },

    handleSaveCard() {
      this.$message.loading('正在保存卡片...', 2);
      setTimeout(() => {
        this.$message.success('卡片已保存到我的收藏');
      }, 2000);
      this.closeMorePopup();
    },

    handleUploadCSV() {
      this.showUploadPopup = true;
      this.closeMorePopup();
    },

    handleDownloadPNG() {
      this.exportData('PNG');
      this.closeMorePopup();
    },

    // 图表按钮处理方法
    handleRefresh() {
      console.log('刷新数据');
      this.$message.loading('正在刷新数据...', 1);
      this.refreshChartData();
    },

    handleDownload() {
      this.exportData('Excel');
    },

    handleSettings(event) {
      console.log('设置');
      this.showSettingsPopup = true;

      this.$nextTick(() => {
        this.setSettingsPopupPosition(event.target);
      });
    },

    // 分享弹窗相关方法
    closeSharePopup() {
      this.showSharePopup = false;
    },

    copyShareLink() {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(this.shareLink).then(() => {
        this.$message.success('链接已复制到剪贴板');
      }).catch(() => {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = this.shareLink;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        this.$message.success('链接已复制到剪贴板');
      });
    },

    // 卡片提醒弹窗方法
    closeReminderPopup() {
      this.showReminderPopup = false;
      // 重置表单
      this.reminderForm = {
        cardName: '',
        email: '',
        changeType: '同比增减幅',
        timePeriod: '天数',
        threshold: 0,
        contentChange: false,
        method: 'email'
      };
    },

    confirmReminder() {
      // 验证表单
      if (!this.reminderForm.cardName) {
        this.$message.warning('请选择提醒卡片');
        return;
      }
      if (!this.reminderForm.email) {
        this.$message.warning('请输入邮箱地址');
        return;
      }

      // 这里可以添加邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(this.reminderForm.email)) {
        this.$message.warning('请输入正确的邮箱格式');
        return;
      }

      console.log('设置卡片提醒:', this.reminderForm);
      this.$message.success('卡片提醒设置成功！');
      this.closeReminderPopup();
    },

    // 上传CSV弹窗方法
    closeUploadPopup() {
      this.showUploadPopup = false;
      // 重置表单
      this.uploadForm = {
        reportName: '',
        description: '',
        file: null
      };
    },

    handleFileSelect(event) {
      const file = event.target.files[0];
      if (file) {
        // 检查文件类型
        if (!file.name.toLowerCase().endsWith('.csv')) {
          this.$message.warning('请选择CSV格式的文件');
          event.target.value = '';
          return;
        }
        this.uploadForm.file = file;
      }
    },

    confirmUpload() {
      // 验证表单
      if (!this.uploadForm.reportName.trim()) {
        this.$message.warning('请输入报告名称');
        return;
      }
      if (!this.uploadForm.file) {
        this.$message.warning('请选择要上传的CSV文件');
        return;
      }

      console.log('上传CSV:', this.uploadForm);
      this.$message.success('CSV文件上传成功！');
      this.closeUploadPopup();
    },

    // 图表选择器相关方法
    openChartSelector() {
      this.showChartSelector = true;
    },

    closeChartSelector() {
      this.showChartSelector = false;
    },

    selectChartType(chartType) {
      this.selectedChartType = chartType;
      console.log('选择图表类型:', chartType);
      this.closeChartSelector();
    },

    // 设置弹窗相关方法
    setSettingsPopupPosition(buttonElement) {
      if (buttonElement) {
        const rect = buttonElement.getBoundingClientRect();
        this.settingsPopupStyle = {
          position: 'fixed',
          top: rect.bottom + 5 + 'px',
          left: rect.left - 100 + 'px',
          zIndex: 2000
        };
      }
    },

    closeSettingsPopup() {
      this.showSettingsPopup = false;
    },

    selectChartIcon(chartType) {
      console.log('选择图表类型:', chartType);
      this.$message.success(`已选择图表类型: ${chartType}`);
      this.closeSettingsPopup();
    },

    // 智能助手相关方法
    sendChatMessage() {
      if (!this.chatInput.trim()) return;

      // 添加用户消息
      this.chatMessages.push({
        type: 'user',
        text: this.chatInput,
        time: this.getCurrentTime()
      });

      const userMessage = this.chatInput;
      this.chatInput = '';

      // 模拟AI回复
      setTimeout(() => {
        this.generateAIResponse(userMessage);
      }, 1000);
    },

    generateAIResponse(userMessage) {
      let response = '';

      if (userMessage.includes('深圳') || userMessage.includes('成功经验')) {
        response = '深圳门店表现优异，营业额达到2.134万元，主要成功因素包括：\n\n1. 地理位置优势 - 位于核心商圈\n2. 客户群体消费能力强\n3. 产品组合策略精准\n4. 服务质量持续优化';
      } else if (userMessage.includes('提升') || userMessage.includes('营业额')) {
        response = '基于深圳门店的成功经验，建议其他门店：\n\n• 学习深圳门店的运营模式\n• 根据当地市场调整产品结构\n• 加强员工培训提升服务水平\n• 优化店面布局和客户体验\n• 制定针对性的营销策略';
      } else if (userMessage.includes('数据分析') || userMessage.includes('分析')) {
        response = '根据当前数据分析显示：\n\n• 华南地区（深圳、广州）表现最佳\n• 华东地区存在下降趋势，需要关注\n• 整体营业额同比增长4.73%\n• 建议重点关注排名靠后的门店';
      } else {
        response = '感谢您的提问！我正在分析相关数据，为您提供专业的建议。您可以询问关于门店营业额、运营策略或数据分析的问题。';
      }

      this.chatMessages.push({
        type: 'assistant',
        text: response,
        time: this.getCurrentTime()
      });
    },

    handleSuggestionClick(suggestion) {
      this.chatInput = suggestion.text;
      this.sendChatMessage();
    },

    getCurrentTime() {
      const now = new Date();
      return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    },

    // 搜索功能增强
    handleSearchEnhanced() {
      this.isLoading = true;
      this.$message.loading('正在搜索数据...', 2);

      // 模拟搜索延迟
      setTimeout(() => {
        this.isLoading = false;
        this.$message.success('搜索完成！');

        // 添加到搜索历史
        if (this.searchForm.keyword && !this.searchHistory.includes(this.searchForm.keyword)) {
          this.searchHistory.unshift(this.searchForm.keyword);
          if (this.searchHistory.length > 5) {
            this.searchHistory.pop();
          }
        }

        // 刷新图表数据
        this.refreshChartData();
      }, 2000);
    },

    // 刷新图表数据
    refreshChartData() {
      // 模拟数据变化
      this.storeRevenueData.revenue = this.storeRevenueData.revenue.map(val =>
        val + Math.floor(Math.random() * 2000 - 1000)
      );
      this.storeRevenueData.profit = this.storeRevenueData.profit.map(val =>
        val + Math.floor(Math.random() * 1500 - 750)
      );
      this.storeRevenueData.growthRate = this.storeRevenueData.growthRate.map(val =>
        +(val + Math.random() * 2 - 1).toFixed(2)
      );

      // 重新渲染图表
      this.initCharts();
    },

    // 数据导出功能
    exportData(format) {
      this.$message.loading(`正在导出${format}格式数据...`, 2);

      setTimeout(() => {
        this.$message.success(`${format}数据导出成功！`);

        // 模拟下载
        const data = {
          门店营业额: this.storeRevenueData,
          品牌营业额: this.cloudRevenueData,
          导出时间: new Date().toLocaleString()
        };

        console.log('导出数据:', data);
      }, 2000);
    },

    // 实时数据更新
    startRealTimeUpdate() {
      if (this.refreshInterval !== '0') {
        const interval = parseInt(this.refreshInterval) * 1000;

        setInterval(() => {
          this.refreshChartData();
          this.$message.info('数据已自动更新');
        }, interval);

        this.$message.success(`已开启${this.refreshInterval}秒自动刷新`);
      }
    },

    // 键盘快捷键处理
    handleKeydown(event) {
      // Ctrl + Enter 执行搜索
      if (event.ctrlKey && event.key === 'Enter') {
        event.preventDefault();
        this.handleSearch();
      }
      // F5 刷新数据
      if (event.key === 'F5') {
        event.preventDefault();
        this.handleRefresh();
      }
      // Ctrl + S 保存卡片
      if (event.ctrlKey && event.key === 's') {
        event.preventDefault();
        this.handleSaveCard();
      }
      // Ctrl + D 下载数据
      if (event.ctrlKey && event.key === 'd') {
        event.preventDefault();
        this.handleDownload();
      }
      // Escape 关闭所有弹窗
      if (event.key === 'Escape') {
        this.closeAllPopups();
      }
    },

    // 关闭所有弹窗
    closeAllPopups() {
      this.showFilterPopup = false;
      this.showMorePopup = false;
      this.showSharePopup = false;
      this.showReminderPopup = false;
      this.showUploadPopup = false;
      this.showChartSelector = false;
      this.showSettingsPopup = false;
    },

    // 图表交互增强
    handleChartClick(params) {
      console.log('图表点击事件:', params);
      this.$message.info(`点击了: ${params.name} - ${params.value}`);
    },

    // 数据筛选功能
    filterDataByCategory(category) {
      this.$message.loading('正在筛选数据...', 1);

      setTimeout(() => {
        // 模拟数据筛选
        if (category === '华南地区') {
          this.storeRevenueData.categories = ['深圳', '广州'];
          this.storeRevenueData.revenue = [21340, 16200];
          this.storeRevenueData.profit = [22410, 18940];
          this.storeRevenueData.growthRate = [11.39, 9.04];
        } else if (category === '华东地区') {
          this.storeRevenueData.categories = ['上海', '杭州', '南京'];
          this.storeRevenueData.revenue = [8100, 7610, 6200];
          this.storeRevenueData.profit = [12400, 7600, 6420];
          this.storeRevenueData.growthRate = [7.60, 5.37, 5.04];
        }

        this.initCharts();
        this.$message.success(`已筛选${category}数据`);
      }, 1000);
    },

    // 重置数据
    resetData() {
      this.storeRevenueData = {
        categories: ['深圳', '广州', '北京', '上海', '杭州', '南京', '成都', '重庆', '武汉', '西安', '天津'],
        revenue: [21340, 16200, 14100, 8100, 7610, 6200, 5310, 4860, 4340, 3460, 3140],
        profit: [22410, 18940, 14200, 12400, 7600, 6420, 5400, 4740, 4360, 3740, 3140],
        growthRate: [11.39, 9.04, 8.31, 7.60, 5.37, 5.04, 4.74, 4.34, 4.17, 3.86, 3.70]
      };

      this.cloudRevenueData = {
        categories: ['华南大区_深圳', '华南大区_广州', '华东大区_上海', '华东大区_杭州', '华北大区_北京', '华中大区_武汉', '西南大区_成都', '西南大区_重庆', '西北大区_西安', '华北大区_天津', '华东大区_南京'],
        revenue: [6000000, 5800000, 4100000, 4100000, 3400000, 2600000, 2400000, 2100000, 2000000, 1900000, 1800000],
        profit: [5800000, 5600000, 4100000, 4100000, 3200000, 2400000, 2200000, 2000000, 1900000, 1800000, 1700000],
        growthRate: [4.70, -0.2, -6.3, -6.3, 1.9, 2.6, 2.7, 2.1, 2.0, 1.9, 1.8]
      };

      this.initCharts();
      this.$message.success('数据已重置');
    },

    // 显示快捷键提示
    showKeyboardShortcuts() {
      setTimeout(() => {
        this.$message.info('💡 快捷键提示：Ctrl+Enter搜索，F5刷新，Ctrl+S保存，Ctrl+D下载，ESC关闭弹窗', 5);
      }, 3000);
    }
  }
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  gap: 20px;
}

// 搜索栏样式
.search-container {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  .search-form {
    display: flex;
    align-items: center;
    gap: 12px;

    .search-input-wrapper {
      flex: 1;

      .search-input {
        width: 100%;
        height: 32px;
        padding: 4px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        font-size: 14px;
        color: #333;
        background: #fff;
        outline: none;
        transition: border-color 0.3s;

        &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        &::placeholder {
          color: #999;
        }
      }
    }

    .search-buttons {
      display: flex;
      align-items: center;
      gap: 8px;

      .btn-icon {
        width: 32px;
        height: 32px;
        padding: 6px;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        background: #fff;
        cursor: pointer;
        transition: all 0.3s;
        display: inline-flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background: #f5f5f5;
          border-color: #40a9ff;
        }

        .close-icon {
          width: 16px;
          height: 16px;
          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDRMNCA0TDQgMTJMMTIgMTJMMTIgNFoiIHN0cm9rZT0iIzY2NiIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K') no-repeat center;
          background-size: contain;
        }

        .search-icon {
          width: 16px;
          height: 16px;
          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTcgMTJDMy42ODYyOSAxMiAxIDkuMzEzNzEgMSA2QzEgMi42ODYyOSAzLjY4NjI5IDAgNyAwQzEwLjMxMzcgMCAxMyAyLjY4NjI5IDEzIDZDMTMgOS4zMTM3MSAxMC4zMTM3IDEyIDcgMTJaTTcgMTFDOS43NjE0MiAxMSAxMiA4Ljc2MTQyIDEyIDZDMTIgMy4yMzg1OCA5Ljc2MTQyIDEgNyAxQzQuMjM4NTggMSAyIDMuMjM4NTggMiA2QzIgOC43NjE0MiA0LjIzODU4IDExIDcgMTFaIiBmaWxsPSIjNjY2Ii8+CjxwYXRoIGQ9Ik0xMSAxMUwxNSAxNSIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=') no-repeat center;
          background-size: contain;
        }

        .minus-icon {
          width: 16px;
          height: 16px;
          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgOEgxMiIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=') no-repeat center;
          background-size: contain;
        }

        .filter-icon {
          width: 16px;
          height: 16px;
          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIgM0gxNEwxMCA3VjEzTDYgMTFWN0wyIDNaIiBzdHJva2U9IiM2NjYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==') no-repeat center;
          background-size: contain;
        }
      }
    }
  }
}

// 筛选弹窗样式
.filter-popup {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  width: 280px;
  max-height: 450px;
  overflow: hidden;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
    border-radius: 6px 6px 0 0;

    span {
      font-size: 14px;
      font-weight: 500;
      color: #262626;
    }

    .popup-close {
      background: none;
      border: none;
      font-size: 16px;
      color: #8c8c8c;
      cursor: pointer;
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 2px;

      &:hover {
        background: #f5f5f5;
        color: #595959;
      }
    }
  }

  .popup-search {
    padding: 12px;
    border-bottom: 1px solid #f0f0f0;
    position: relative;

    .search-input {
      width: 100%;
      padding: 6px 12px 6px 32px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 13px;
      outline: none;
      background: #ffffff;

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }

      &::placeholder {
        color: #bfbfbf;
      }
    }

    .search-icon {
      position: absolute;
      left: 20px;
      top: 50%;
      transform: translateY(-50%);
      width: 14px;
      height: 14px;
      background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23999" stroke-width="2"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/></svg>') no-repeat center;
      background-size: contain;
    }
  }

  .popup-tabs {
    display: flex;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;

    .tab-item {
      flex: 1;
      padding: 8px 12px;
      text-align: center;
      font-size: 13px;
      color: #595959;
      cursor: pointer;
      transition: all 0.2s;
      border-bottom: 2px solid transparent;

      &:hover {
        color: #1890ff;
        background: #f5f5f5;
      }

      &.active {
        color: #1890ff;
        background: #ffffff;
        border-bottom-color: #1890ff;
        font-weight: 500;
      }
    }
  }

  .popup-content {
    padding: 0;
    max-height: 300px;
    overflow-y: auto;

    .tab-content {
      .filter-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 16px;
        font-size: 14px;
        color: #262626;
        cursor: pointer;
        transition: all 0.2s;
        border-bottom: 1px solid #f5f5f5;
        line-height: 1.4;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: #f0f8ff;
          color: #1890ff;
        }

        &:active {
          background: #e6f7ff;
          color: #1890ff;
        }

        .arrow-icon {
          font-size: 12px;
          color: #8c8c8c;
          font-style: normal;
        }
      }

      .time-units-row {
        padding: 10px 16px;
        border-bottom: 1px solid #f5f5f5;
        display: flex;
        gap: 16px;

        .time-unit {
          font-size: 14px;
          color: #262626;
          cursor: pointer;
          transition: all 0.2s;
          padding: 4px 8px;
          border-radius: 4px;

          &:hover {
            background: #f0f8ff;
            color: #1890ff;
          }
        }
      }

      .time-item {
        padding: 10px 16px;
        font-size: 14px;
        color: #262626;
        cursor: pointer;
        transition: all 0.2s;
        border-bottom: 1px solid #f5f5f5;
        line-height: 1.4;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background: #f0f8ff;
          color: #1890ff;
        }
      }
    }
  }
}

.search-form {
  .form-row {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }

  .form-group {
    display: flex;
    align-items: center;
    gap: 6px;

    .form-label {
      font-size: 13px;
      color: #595959;
      font-weight: 400;
      white-space: nowrap;
      margin: 0;
    }

    .form-input {
      height: 28px;
      padding: 0 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 13px;
      color: #262626;
      background: #ffffff;
      outline: none;
      transition: border-color 0.2s;

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }

      &.keyword-input {
        width: 200px;
      }

      &.time-input {
        width: 60px;
      }

      &::placeholder {
        color: #bfbfbf;
        font-size: 13px;
      }
    }

    .form-select {
      height: 28px;
      padding: 0 20px 0 8px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 13px;
      color: #262626;
      background: #ffffff url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") no-repeat right 6px center/12px 12px;
      outline: none;
      appearance: none;
      cursor: pointer;
      min-width: 80px;
      transition: border-color 0.2s;

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }
    }
  }

  .form-buttons {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;

    .btn {
      height: 28px;
      padding: 0 12px;
      border-radius: 4px;
      font-size: 13px;
      font-weight: 400;
      border: 1px solid;
      cursor: pointer;
      outline: none;
      transition: all 0.2s;
      white-space: nowrap;

      &.btn-primary {
        background: #1890ff;
        border-color: #1890ff;
        color: #ffffff;

        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
        }
      }

      &.btn-default {
        background: #ffffff;
        border-color: #d9d9d9;
        color: #595959;

        &:hover {
          color: #1890ff;
          border-color: #1890ff;
        }
      }

      &.btn-success {
        background: #52c41a;
        border-color: #52c41a;
        color: #ffffff;

        &:hover {
          background: #73d13d;
          border-color: #73d13d;
        }
      }
    }
  }
}

// 顶部区域：门店营业额前十的 + 智能助手
.top-section {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

// 中间区域：营业额同比 单独一行
.middle-section {
  width: 100%;
}

// 底部区域：品牌门店营业额前十的 + 智能助手
.bottom-section {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

// 主图表区域
.main-chart {
  flex: 1;
  min-width: 0;
}

// 智能助手面板
.assistant-panel {
  width: 280px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  height: 500px;

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafbfc;
    flex-shrink: 0;

    span {
      font-size: 14px;
      font-weight: 500;
      color: #262626;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 8px;

      .send-btn {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: #1890ff;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s;

        &:hover {
          background: #40a9ff;
          transform: scale(1.05);
        }

        &:active {
          background: #096dd9;
          transform: scale(0.95);
        }
      }

      .panel-close {
        cursor: pointer;
        font-size: 16px;
        color: #8c8c8c;

        &:hover {
          color: #262626;
        }
      }
    }
  }

  .panel-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;

    .chat-messages {
      flex: 1;
      padding: 16px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 12px;

      .message-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;

        &.user-message {
          flex-direction: row-reverse;

          .message-content {
            background: #1890ff;
            color: white;
            border-radius: 12px 12px 4px 12px;
            max-width: 70%;
          }
        }

        &.assistant-message {
          .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #f0f8ff;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            .avatar-circle {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              background: #e6f7ff;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }

          .message-content {
            background: #f5f5f5;
            color: #262626;
            border-radius: 12px 12px 12px 4px;
            max-width: 70%;
          }
        }

        .message-content {
          padding: 8px 12px;

          .message-text {
            font-size: 13px;
            line-height: 1.4;
            margin-bottom: 4px;
          }

          .message-time {
            font-size: 11px;
            opacity: 0.7;
          }
        }
      }

      .suggestion-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        margin: 4px 0;
        background: #f8f9fa;
        border-radius: 6px;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background: #e9ecef;
        }

        .suggestion-icon {
          margin-right: 8px;
          font-size: 14px;
        }

        .suggestion-text {
          font-size: 13px;
          color: #666;
        }
      }
    }

    .input-area {
      padding: 12px 16px;
      border-top: 1px solid #f0f0f0;
      background: #fafbfc;
      flex-shrink: 0;

      .input-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;
        background: white;
        border: 1px solid #d9d9d9;
        border-radius: 20px;
        padding: 6px 12px;

        &:focus-within {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .chat-input {
          flex: 1;
          border: none;
          outline: none;
          font-size: 13px;
          padding: 4px 0;
          background: transparent;

          &::placeholder {
            color: #bfbfbf;
          }
        }

        .input-send-btn {
          width: 24px;
          height: 24px;
          border: none;
          background: transparent;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: all 0.2s;

          &:hover {
            background: #f0f8ff;
          }

          &:active {
            background: #e6f7ff;
          }
        }
      }
    }
  }
}

.chart-card, .value-card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header, .value-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafbfc;
}

.chart-title, .value-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #262626;

  .chart-icon {
    width: 16px;
    height: 16px;
    background: #1890ff;
    border-radius: 2px;
  }

  .help-icon {
    width: 16px;
    height: 16px;
    background: #d9d9d9;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: #ffffff;
    cursor: pointer;
  }
}

.chart-meta, .value-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #8c8c8c;

  .chart-date, .value-date {
    color: #595959;
  }

  .chart-type, .value-type {
    background: #f0f0f0;
    padding: 2px 6px;
    border-radius: 2px;
  }

  .chart-source {
    color: #1890ff;
  }
}

.chart-actions, .value-actions {
  display: flex;
  align-items: center;
  gap: 8px;

  .action-icon {
    width: 16px;
    height: 16px;
    border-radius: 2px;
    cursor: pointer;
    background-size: 12px 12px;
    background-position: center;
    background-repeat: no-repeat;
    transition: all 0.2s;

    &.refresh {
      background-color: #52c41a;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'/%3e%3c/svg%3e");

      &:hover {
        background-color: #73d13d;
      }
    }

    &.download {
      background-color: #1890ff;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'/%3e%3c/svg%3e");

      &:hover {
        background-color: #40a9ff;
      }
    }

    &.more {
      background-color: #8c8c8c;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z'/%3e%3c/svg%3e");

      &:hover {
        background-color: #a6a6a6;
      }
    }

    &.settings {
      background-color: #722ed1;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3crect x='3' y='12' width='4' height='9'/%3e%3crect x='10' y='8' width='4' height='13'/%3e%3crect x='17' y='4' width='4' height='17'/%3e%3c/svg%3e");

      &:hover {
        background-color: #9254de;
      }
    }

    &.close {
      background-color: #ff4d4f;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M6 18L18 6M6 6l12 12'/%3e%3c/svg%3e");

      &:hover {
        background-color: #ff7875;
      }
    }
  }

  .chart-status {
    font-size: 12px;
    color: #8c8c8c;
    margin-left: 8px;
  }
}

.chart-content {
  padding: 20px;
}

.chart-legend {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 16px;
  font-size: 12px;

  .legend-item {
    display: flex;
    align-items: center;
    gap: 6px;

    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;

      &.blue {
        background: #5B8FF9;
      }

      &.yellow {
        background: #FFD666;
      }

      &.line {
        background: #FF6B6B;
        border-radius: 50%;
        width: 8px;
        height: 8px;
      }
    }
  }
}

.chart-wrapper {
  width: 100%;
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.value-content {
  padding: 20px;
}

.value-main {
  .value-label {
    font-size: 12px;
    color: #8c8c8c;
    margin-bottom: 8px;
    display: block;
  }

  .value-number {
    font-size: 36px;
    font-weight: bold;
    color: #262626;
    line-height: 1;
    margin-bottom: 12px;

    .value-unit {
      font-size: 18px;
      color: #8c8c8c;
      margin-left: 4px;
    }
  }

  .value-change {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;

    .change-text {
      color: #8c8c8c;
    }

    .change-value {
      &.positive {
        color: #52c41a;
      }

      &.negative {
        color: #ff4d4f;
      }
    }

    .change-arrow {
      width: 0;
      height: 0;

      &.up {
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 6px solid #52c41a;
      }

      &.down {
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 6px solid #ff4d4f;
      }
    }
  }
}

.control-panel {
  position: fixed;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 280px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  z-index: 1000;

  .panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafbfc;
    border-radius: 8px 8px 0 0;

    span {
      font-size: 14px;
      font-weight: 500;
      color: #262626;
    }

    .panel-close {
      cursor: pointer;
      font-size: 16px;
      color: #8c8c8c;
    }
  }

  .panel-content {
    padding: 20px;

    .panel-section {
      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: #262626;
      }

      .setting-item {
        margin-bottom: 16px;

        label {
          display: block;
          margin-bottom: 6px;
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .search-container {
    .search-form {
      .form-row {
        gap: 12px;
      }

      .form-group {
        .form-input.keyword-input {
          width: 160px;
        }
      }

      .form-buttons {
        margin-left: 0;
        margin-top: 8px;
        width: 100%;
        justify-content: flex-start;
      }
    }
  }

  .top-section, .bottom-section {
    flex-direction: column;
    gap: 15px;
  }

  .assistant-panel {
    width: 100%;
    order: -1; // 智能助手面板在移动端显示在图表上方
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
    gap: 15px;
  }

  .search-container {
    .search-form {
      .form-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .form-group {
        width: 100%;

        .form-input {
          flex: 1;
          min-width: 120px;

          &.keyword-input {
            width: 100%;
          }
        }

        .form-select {
          flex: 1;
          min-width: 120px;
        }
      }

      .form-buttons {
        width: 100%;
        justify-content: center;
        margin-top: 12px;
      }
    }
  }

  .top-section, .bottom-section {
    flex-direction: column;
    gap: 10px;
  }

  .assistant-panel {
    width: 100%;
    order: -1;

    .panel-content {
      padding: 15px;

      .assistant-item {
        padding: 10px 0;

        .assistant-icon {
          width: 28px;
          height: 28px;
          font-size: 16px;
        }

        .assistant-text {
          font-size: 13px;
        }
      }
    }
  }

  .chart-header, .value-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 12px 16px;
  }

  .chart-meta, .value-meta {
    order: 1;
  }

  .chart-actions, .value-actions {
    order: 2;
    align-self: flex-end;
  }

  .chart-content {
    padding: 16px;
  }

  .value-content {
    padding: 16px;
  }

  .chart-wrapper {
    height: 250px;
  }

  .value-number {
    font-size: 28px !important;

    .value-unit {
      font-size: 14px !important;
    }
  }
}

// 更多操作弹窗样式
.more-popup {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 160px;
  overflow: hidden;
  position: fixed;
  z-index: 2000;

  .more-popup-content {
    .more-action-item {
      padding: 12px 16px;
      font-size: 14px;
      color: #262626;
      cursor: pointer;
      transition: all 0.2s;
      border-bottom: 1px solid #f5f5f5;
      line-height: 1.4;

      &:last-child {
        border-bottom: none;
      }

      &:hover {
        background: #f0f8ff;
        color: #1890ff;
      }

      &:active {
        background: #e6f7ff;
        color: #1890ff;
      }
    }
  }
}

// 分享弹窗样式
.share-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-popup {
  background: white;
  border-radius: 8px;
  width: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.share-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.share-popup-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.share-popup-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #666;
  }
}

.share-popup-content {
  padding: 20px;
}

.share-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
  line-height: 1.5;
}

.share-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.share-option-label {
  font-size: 14px;
  color: #333;
}

.share-toggle {
  position: relative;
}

.toggle-input {
  display: none;
}

.toggle-label {
  display: block;
  width: 44px;
  height: 24px;
  background: #ddd;
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  transition: background 0.3s;

  &::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s;
  }
}

.toggle-input:checked + .toggle-label {
  background: #1890ff;

  &::after {
    transform: translateX(20px);
  }
}

.share-link-section {
  display: flex;
  gap: 8px;
}

.share-link-input {
  flex: 1;
  height: 36px;
  padding: 0 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  color: #666;
  background: #f5f5f5;
  outline: none;
}

.copy-link-btn {
  height: 36px;
  padding: 0 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.3s;

  &:hover {
    background: #40a9ff;
  }

  &:active {
    background: #096dd9;
  }
}

// 卡片提醒弹窗样式
.reminder-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reminder-popup {
  background: white;
  border-radius: 8px;
  width: 520px;
  max-width: 90vw;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  z-index: 10001 !important;
  position: relative;
}

.reminder-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.reminder-popup-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.reminder-popup-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #666;
  }
}

.reminder-popup-content {
  padding: 20px;
}

.reminder-form-item {
  margin-bottom: 16px;

  .reminder-label {
    display: block;
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .reminder-select {
    width: 100%;
    height: 36px;
    padding: 0 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    background: white;
    outline: none;
    cursor: pointer;

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .reminder-input {
    width: 100%;
    height: 36px;
    padding: 0 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    background: white;
    outline: none;

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &::placeholder {
      color: #bfbfbf;
    }
  }

  .reminder-change-section {
    display: flex;
    gap: 12px;

    .reminder-select-small {
      flex: 1;
      height: 36px;
      padding: 0 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 14px;
      color: #333;
      background: white;
      outline: none;
      cursor: pointer;

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }

  .reminder-threshold-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .reminder-number-input {
      width: 120px;
      height: 36px;
      padding: 0 12px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      font-size: 14px;
      color: #333;
      background: white;
      outline: none;

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }

    .reminder-unit {
      font-size: 14px;
      color: #666;
    }

    .reminder-checkbox-section {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-left: auto;

      .reminder-checkbox {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }

      .reminder-checkbox-label {
        font-size: 14px;
        color: #333;
        cursor: pointer;
        margin: 0;
      }
    }
  }

  .reminder-method-section {
    display: flex;
    gap: 20px;

    .reminder-radio-item {
      display: flex;
      align-items: center;
      gap: 6px;

      .reminder-radio {
        width: 16px;
        height: 16px;
        cursor: pointer;
      }

      .reminder-radio-label {
        font-size: 14px;
        color: #333;
        cursor: pointer;
        margin: 0;
      }
    }
  }
}

.reminder-description {
  font-size: 13px;
  color: #999;
  margin: 16px 0;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  line-height: 1.5;
}

.reminder-popup-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.reminder-cancel-btn {
  height: 36px;
  padding: 0 16px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    color: #1890ff;
    border-color: #1890ff;
  }
}

.reminder-confirm-btn {
  height: 36px;
  padding: 0 16px;
  background: #1890ff;
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.3s;

  &:hover {
    background: #40a9ff;
  }

  &:active {
    background: #096dd9;
  }
}

// 上传CSV弹窗样式
.upload-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-popup {
  background: white;
  border-radius: 8px;
  width: 480px;
  max-width: 90vw;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.upload-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.upload-popup-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.upload-popup-close {
  background: none;
  border: none;
  font-size: 20px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #666;
  }
}

.upload-popup-content {
  padding: 20px;
}

.upload-form-item {
  margin-bottom: 16px;

  .upload-label {
    display: block;
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .upload-input {
    width: 100%;
    height: 36px;
    padding: 0 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    background: white;
    outline: none;

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &::placeholder {
      color: #bfbfbf;
    }
  }

  .upload-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    background: white;
    outline: none;
    resize: vertical;
    min-height: 80px;

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &::placeholder {
      color: #bfbfbf;
    }
  }

  .upload-file-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .upload-file-input {
      display: none;
    }

    .upload-file-button {
      height: 36px;
      padding: 0 16px;
      background: #1890ff;
      border: none;
      border-radius: 4px;
      color: white;
      font-size: 14px;
      cursor: pointer;
      transition: background 0.3s;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover {
        background: #40a9ff;
      }

      &:active {
        background: #096dd9;
      }
    }

    .upload-file-name {
      font-size: 14px;
      color: #333;
      flex: 1;
    }

    .upload-file-placeholder {
      font-size: 14px;
      color: #bfbfbf;
      flex: 1;
    }
  }
}

.upload-tips {
  margin-top: 20px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #1890ff;

  .upload-tips-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
  }

  .upload-tips-content {
    font-size: 13px;
    color: #666;
    line-height: 1.6;
  }
}

.upload-popup-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.upload-cancel-btn {
  height: 36px;
  padding: 0 16px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    color: #1890ff;
    border-color: #1890ff;
  }
}

.upload-confirm-btn {
  height: 36px;
  padding: 0 16px;
  background: #1890ff;
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.3s;

  &:hover {
    background: #40a9ff;
  }

  &:active {
    background: #096dd9;
  }
}

// 设置弹窗样式
.settings-popup {
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px;
  overflow: hidden;

  .settings-popup-content {
    .chart-types-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;

      .chart-type-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 8px 4px;
        cursor: pointer;
        transition: all 0.2s;
        border-radius: 4px;
        min-height: 60px;

        &:hover {
          background: #f0f8ff;
          transform: translateY(-2px);
        }

        &:active {
          background: #e6f7ff;
          transform: translateY(0);
        }

        .chart-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 4px;

          svg {
            width: 20px;
            height: 20px;
            transition: all 0.2s;
          }
        }

        .chart-label {
          font-size: 12px;
          color: #262626;
          text-align: center;
          line-height: 1.2;
          white-space: nowrap;
        }

        &:hover {
          .chart-icon svg {
            transform: scale(1.1);
          }

          .chart-label {
            color: #1890ff;
          }
        }
      }
    }
  }
}
</style>
