import request from '@/utils/request'

// 查询数据搜索列表
export function listDataSearch(query) {
  return request({
    url: '/datasearch/list',
    method: 'get',
    params: query
  })
}

// 查询数据搜索详细
export function getDataSearch(id) {
  return request({
    url: '/datasearch/' + id,
    method: 'get'
  })
}

// 新增数据搜索
export function addDataSearch(data) {
  return request({
    url: '/datasearch',
    method: 'post',
    data: data
  })
}

// 修改数据搜索
export function updateDataSearch(data) {
  return request({
    url: '/datasearch',
    method: 'put',
    data: data
  })
}

// 删除数据搜索
export function delDataSearch(id) {
  return request({
    url: '/datasearch/' + id,
    method: 'delete'
  })
}

// 导出数据搜索
export function exportDataSearch(query) {
  return request({
    url: '/datasearch/export',
    method: 'get',
    params: query
  })
}

// 获取数据统计信息
export function getDataStatistics() {
  return request({
    url: '/datasearch/statistics',
    method: 'get'
  })
}

// 下载数据文件
export function downloadDataFile(id) {
  return request({
    url: '/datasearch/download/' + id,
    method: 'get',
    responseType: 'blob'
  })
}

// 批量删除数据
export function batchDelDataSearch(ids) {
  return request({
    url: '/datasearch/batch',
    method: 'delete',
    data: ids
  })
}

// 搜索数据
export function searchData(query) {
  return request({
    url: '/datasearch/search',
    method: 'post',
    data: query
  })
}
