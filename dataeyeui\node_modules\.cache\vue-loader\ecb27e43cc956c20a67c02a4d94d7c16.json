{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\src\\views\\datasearch\\index.vue?vue&type=template&id=b84ac022&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\src\\views\\datasearch\\index.vue", "mtime": 1748245473679}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748221550136}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748221555438}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748221550136}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748221554057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}