{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\src\\views\\datasearch\\index.vue?vue&type=template&id=b84ac022&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\src\\views\\datasearch\\index.vue", "mtime": 1748242653699}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748221550136}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1748221555438}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748221550136}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748221554057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}