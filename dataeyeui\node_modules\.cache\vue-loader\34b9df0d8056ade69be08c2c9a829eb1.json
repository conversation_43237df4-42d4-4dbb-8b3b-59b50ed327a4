{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\src\\views\\datasearch\\index.vue?vue&type=style&index=0&id=b84ac022&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\src\\views\\datasearch\\index.vue", "mtime": 1748242939721}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1748221551307}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1748221555328}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1748221552813}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1748221550139}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748221550136}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748221554057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi5kYXNoYm9hcmQtY29udGFpbmVyIHsKICBwYWRkaW5nOiAyMHB4OwogIGJhY2tncm91bmQtY29sb3I6ICNmNWY3ZmE7CiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDg0cHgpOwogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBnYXA6IDIwcHg7Cn0KCi8vIOaQnOe0ouagj+agt+W8jwouc2VhcmNoLWNvbnRhaW5lciB7CiAgYmFja2dyb3VuZDogI2ZmZmZmZjsKICBib3JkZXI6IDFweCBzb2xpZCAjZThlOGU4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBwYWRkaW5nOiAxMnB4IDE2cHg7CiAgbWFyZ2luLWJvdHRvbTogMTZweDsKICBib3gtc2hhZG93OiAwIDFweCAzcHggcmdiYSgwLCAwLCAwLCAwLjEpOwoKICAuc2VhcmNoLWZvcm0gewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBnYXA6IDEycHg7CgogICAgLnNlYXJjaC1pbnB1dC13cmFwcGVyIHsKICAgICAgZmxleDogMTsKCiAgICAgIC5zZWFyY2gtaW5wdXQgewogICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgIGhlaWdodDogMzJweDsKICAgICAgICBwYWRkaW5nOiA0cHggMTJweDsKICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5OwogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgICAgY29sb3I6ICMzMzM7CiAgICAgICAgYmFja2dyb3VuZDogI2ZmZjsKICAgICAgICBvdXRsaW5lOiBub25lOwogICAgICAgIHRyYW5zaXRpb246IGJvcmRlci1jb2xvciAwLjNzOwoKICAgICAgICAmOmZvY3VzIHsKICAgICAgICAgIGJvcmRlci1jb2xvcjogIzE4OTBmZjsKICAgICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDI0LCAxNDQsIDI1NSwgMC4yKTsKICAgICAgICB9CgogICAgICAgICY6OnBsYWNlaG9sZGVyIHsKICAgICAgICAgIGNvbG9yOiAjOTk5OwogICAgICAgIH0KICAgICAgfQogICAgfQoKICAgIC5zZWFyY2gtYnV0dG9ucyB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIGdhcDogOHB4OwoKICAgICAgLmJ0bi1pY29uIHsKICAgICAgICB3aWR0aDogMzJweDsKICAgICAgICBoZWlnaHQ6IDMycHg7CiAgICAgICAgcGFkZGluZzogNnB4OwogICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7CiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICAgIGJhY2tncm91bmQ6ICNmZmY7CiAgICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzOwogICAgICAgIGRpc3BsYXk6IGlubGluZS1mbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CgogICAgICAgICY6aG92ZXIgewogICAgICAgICAgYmFja2dyb3VuZDogI2Y1ZjVmNTsKICAgICAgICAgIGJvcmRlci1jb2xvcjogIzQwYTlmZjsKICAgICAgICB9CgogICAgICAgIC5jbG9zZS1pY29uIHsKICAgICAgICAgIHdpZHRoOiAxNnB4OwogICAgICAgICAgaGVpZ2h0OiAxNnB4OwogICAgICAgICAgYmFja2dyb3VuZDogdXJsKCdkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUIzYVdSMGFEMGlNVFlpSUdobGFXZG9kRDBpTVRZaUlIWnBaWGRDYjNnOUlqQWdNQ0F4TmlBeE5pSWdabWxzYkQwaWJtOXVaU0lnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JajRLUEhCaGRHZ2daRDBpVFRFeUlEUk1OQ0EwVERRZ01USk1NVElnTVRKTU1USWdORm9pSUhOMGNtOXJaVDBpSXpZMk5pSWdjM1J5YjJ0bExYZHBaSFJvUFNJeExqVWlJSE4wY205clpTMXNhVzVsWTJGd1BTSnliM1Z1WkNJZ2MzUnliMnRsTFd4cGJtVnFiMmx1UFNKeWIzVnVaQ0l2UGdvOEwzTjJaejRLJykgbm8tcmVwZWF0IGNlbnRlcjsKICAgICAgICAgIGJhY2tncm91bmQtc2l6ZTogY29udGFpbjsKICAgICAgICB9CgogICAgICAgIC5zZWFyY2gtaWNvbiB7CiAgICAgICAgICB3aWR0aDogMTZweDsKICAgICAgICAgIGhlaWdodDogMTZweDsKICAgICAgICAgIGJhY2tncm91bmQ6IHVybCgnZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlCM2FXUjBhRDBpTVRZaUlHaGxhV2RvZEQwaU1UWWlJSFpwWlhkQ2IzZzlJakFnTUNBeE5pQXhOaUlnWm1sc2JEMGlibTl1WlNJZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWo0S1BIQmhkR2dnWkQwaVRUY2dNVEpETXk0Mk9EWXlPU0F4TWlBeElEa3VNekV6TnpFZ01TQTJRekVnTWk0Mk9EWXlPU0F6TGpZNE5qSTVJREFnTnlBd1F6RXdMak14TXpjZ01DQXhNeUF5TGpZNE5qSTVJREV6SURaRE1UTWdPUzR6TVRNM01TQXhNQzR6TVRNM0lERXlJRGNnTVRKYVRUY2dNVEZET1M0M05qRTBNaUF4TVNBeE1pQTRMamMyTVRReUlERXlJRFpETVRJZ015NHlNemcxT0NBNUxqYzJNVFF5SURFZ055QXhRelF1TWpNNE5UZ2dNU0F5SURNdU1qTTROVGdnTWlBMlF6SWdPQzQzTmpFME1pQTBMakl6T0RVNElERXhJRGNnTVRGYUlpQm1hV3hzUFNJak5qWTJJaTgrQ2p4d1lYUm9JR1E5SWsweE1TQXhNVXd4TlNBeE5TSWdjM1J5YjJ0bFBTSWpOalkySWlCemRISnZhMlV0ZDJsa2RHZzlJakV1TlNJZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJaTgrQ2p3dmMzWm5QZ289Jykgbm8tcmVwZWF0IGNlbnRlcjsKICAgICAgICAgIGJhY2tncm91bmQtc2l6ZTogY29udGFpbjsKICAgICAgICB9CgogICAgICAgIC5taW51cy1pY29uIHsKICAgICAgICAgIHdpZHRoOiAxNnB4OwogICAgICAgICAgaGVpZ2h0OiAxNnB4OwogICAgICAgICAgYmFja2dyb3VuZDogdXJsKCdkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUIzYVdSMGFEMGlNVFlpSUdobGFXZG9kRDBpTVRZaUlIWnBaWGRDYjNnOUlqQWdNQ0F4TmlBeE5pSWdabWxzYkQwaWJtOXVaU0lnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JajRLUEhCaGRHZ2daRDBpVFRRZ09FZ3hNaUlnYzNSeWIydGxQU0lqTmpZMklpQnpkSEp2YTJVdGQybGtkR2c5SWpFdU5TSWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWk4K0Nqd3ZjM1puUGdvPScpIG5vLXJlcGVhdCBjZW50ZXI7CiAgICAgICAgICBiYWNrZ3JvdW5kLXNpemU6IGNvbnRhaW47CiAgICAgICAgfQoKICAgICAgICAuZmlsdGVyLWljb24gewogICAgICAgICAgd2lkdGg6IDE2cHg7CiAgICAgICAgICBoZWlnaHQ6IDE2cHg7CiAgICAgICAgICBiYWNrZ3JvdW5kOiB1cmwoJ2RhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QjNhV1IwYUQwaU1UWWlJR2hsYVdkb2REMGlNVFlpSUhacFpYZENiM2c5SWpBZ01DQXhOaUF4TmlJZ1ptbHNiRDBpYm05dVpTSWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklqNEtQSEJoZEdnZ1pEMGlUVElnTTBneE5Fd3hNQ0EzVmpFelREWWdNVEZXTjB3eUlETmFJaUJ6ZEhKdmEyVTlJaU0yTmpZaUlITjBjbTlyWlMxM2FXUjBhRDBpTVM0MUlpQnpkSEp2YTJVdGJHbHVaV05oY0QwaWNtOTFibVFpSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlMejRLUEM5emRtYytDZz09Jykgbm8tcmVwZWF0IGNlbnRlcjsKICAgICAgICAgIGJhY2tncm91bmQtc2l6ZTogY29udGFpbjsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0KCi8vIOetm+mAieW8ueeql+agt+W8jwouZmlsdGVyLXBvcHVwIHsKICBiYWNrZ3JvdW5kOiAjZmZmZmZmOwogIGJvcmRlcjogMXB4IHNvbGlkICNlOGU4ZTg7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjE1KTsKICB3aWR0aDogMjgwcHg7CiAgbWF4LWhlaWdodDogNDUwcHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKCiAgLnBvcHVwLWhlYWRlciB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIHBhZGRpbmc6IDhweCAxMnB4OwogICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7CiAgICBiYWNrZ3JvdW5kOiAjZmFmYWZhOwogICAgYm9yZGVyLXJhZGl1czogNnB4IDZweCAwIDA7CgogICAgc3BhbiB7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgZm9udC13ZWlnaHQ6IDUwMDsKICAgICAgY29sb3I6ICMyNjI2MjY7CiAgICB9CgogICAgLnBvcHVwLWNsb3NlIHsKICAgICAgYmFja2dyb3VuZDogbm9uZTsKICAgICAgYm9yZGVyOiBub25lOwogICAgICBmb250LXNpemU6IDE2cHg7CiAgICAgIGNvbG9yOiAjOGM4YzhjOwogICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgIHBhZGRpbmc6IDA7CiAgICAgIHdpZHRoOiAyMHB4OwogICAgICBoZWlnaHQ6IDIwcHg7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgICBib3JkZXItcmFkaXVzOiAycHg7CgogICAgICAmOmhvdmVyIHsKICAgICAgICBiYWNrZ3JvdW5kOiAjZjVmNWY1OwogICAgICAgIGNvbG9yOiAjNTk1OTU5OwogICAgICB9CiAgICB9CiAgfQoKICAucG9wdXAtc2VhcmNoIHsKICAgIHBhZGRpbmc6IDEycHg7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsKICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKCiAgICAuc2VhcmNoLWlucHV0IHsKICAgICAgd2lkdGg6IDEwMCU7CiAgICAgIHBhZGRpbmc6IDZweCAxMnB4IDZweCAzMnB4OwogICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5OwogICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgIGZvbnQtc2l6ZTogMTNweDsKICAgICAgb3V0bGluZTogbm9uZTsKICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjsKCiAgICAgICY6Zm9jdXMgewogICAgICAgIGJvcmRlci1jb2xvcjogIzE4OTBmZjsKICAgICAgICBib3gtc2hhZG93OiAwIDAgMCAycHggcmdiYSgyNCwgMTQ0LCAyNTUsIDAuMSk7CiAgICAgIH0KCiAgICAgICY6OnBsYWNlaG9sZGVyIHsKICAgICAgICBjb2xvcjogI2JmYmZiZjsKICAgICAgfQogICAgfQoKICAgIC5zZWFyY2gtaWNvbiB7CiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgbGVmdDogMjBweDsKICAgICAgdG9wOiA1MCU7CiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKTsKICAgICAgd2lkdGg6IDE0cHg7CiAgICAgIGhlaWdodDogMTRweDsKICAgICAgYmFja2dyb3VuZDogdXJsKCdkYXRhOmltYWdlL3N2Zyt4bWw7dXRmOCw8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiUyMzk5OSIgc3Ryb2tlLXdpZHRoPSIyIj48Y2lyY2xlIGN4PSIxMSIgY3k9IjExIiByPSI4Ii8+PHBhdGggZD0ibTIxIDIxLTQuMzUtNC4zNSIvPjwvc3ZnPicpIG5vLXJlcGVhdCBjZW50ZXI7CiAgICAgIGJhY2tncm91bmQtc2l6ZTogY29udGFpbjsKICAgIH0KICB9CgogIC5wb3B1cC10YWJzIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsKICAgIGJhY2tncm91bmQ6ICNmYWZhZmE7CgogICAgLnRhYi1pdGVtIHsKICAgICAgZmxleDogMTsKICAgICAgcGFkZGluZzogOHB4IDEycHg7CiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgZm9udC1zaXplOiAxM3B4OwogICAgICBjb2xvcjogIzU5NTk1OTsKICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICB0cmFuc2l0aW9uOiBhbGwgMC4yczsKICAgICAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkIHRyYW5zcGFyZW50OwoKICAgICAgJjpob3ZlciB7CiAgICAgICAgY29sb3I6ICMxODkwZmY7CiAgICAgICAgYmFja2dyb3VuZDogI2Y1ZjVmNTsKICAgICAgfQoKICAgICAgJi5hY3RpdmUgewogICAgICAgIGNvbG9yOiAjMTg5MGZmOwogICAgICAgIGJhY2tncm91bmQ6ICNmZmZmZmY7CiAgICAgICAgYm9yZGVyLWJvdHRvbS1jb2xvcjogIzE4OTBmZjsKICAgICAgICBmb250LXdlaWdodDogNTAwOwogICAgICB9CiAgICB9CiAgfQoKICAucG9wdXAtY29udGVudCB7CiAgICBwYWRkaW5nOiAwOwogICAgbWF4LWhlaWdodDogMzAwcHg7CiAgICBvdmVyZmxvdy15OiBhdXRvOwoKICAgIC50YWItY29udGVudCB7CiAgICAgIC5maWx0ZXItaXRlbSB7CiAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICBwYWRkaW5nOiAxMHB4IDE2cHg7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIGNvbG9yOiAjMjYyNjI2OwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4yczsKICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2Y1ZjVmNTsKICAgICAgICBsaW5lLWhlaWdodDogMS40OwoKICAgICAgICAmOmxhc3QtY2hpbGQgewogICAgICAgICAgYm9yZGVyLWJvdHRvbTogbm9uZTsKICAgICAgICB9CgogICAgICAgICY6aG92ZXIgewogICAgICAgICAgYmFja2dyb3VuZDogI2YwZjhmZjsKICAgICAgICAgIGNvbG9yOiAjMTg5MGZmOwogICAgICAgIH0KCiAgICAgICAgJjphY3RpdmUgewogICAgICAgICAgYmFja2dyb3VuZDogI2U2ZjdmZjsKICAgICAgICAgIGNvbG9yOiAjMTg5MGZmOwogICAgICAgIH0KCiAgICAgICAgLmFycm93LWljb24gewogICAgICAgICAgZm9udC1zaXplOiAxMnB4OwogICAgICAgICAgY29sb3I6ICM4YzhjOGM7CiAgICAgICAgICBmb250LXN0eWxlOiBub3JtYWw7CiAgICAgICAgfQogICAgICB9CgogICAgICAudGltZS11bml0cy1yb3cgewogICAgICAgIHBhZGRpbmc6IDEwcHggMTZweDsKICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2Y1ZjVmNTsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGdhcDogMTZweDsKCiAgICAgICAgLnRpbWUtdW5pdCB7CiAgICAgICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgICAgICBjb2xvcjogIzI2MjYyNjsKICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzOwogICAgICAgICAgcGFkZGluZzogNHB4IDhweDsKICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKCiAgICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgICAgYmFja2dyb3VuZDogI2YwZjhmZjsKICAgICAgICAgICAgY29sb3I6ICMxODkwZmY7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9CgogICAgICAudGltZS1pdGVtIHsKICAgICAgICBwYWRkaW5nOiAxMHB4IDE2cHg7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIGNvbG9yOiAjMjYyNjI2OwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4yczsKICAgICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2Y1ZjVmNTsKICAgICAgICBsaW5lLWhlaWdodDogMS40OwoKICAgICAgICAmOmxhc3QtY2hpbGQgewogICAgICAgICAgYm9yZGVyLWJvdHRvbTogbm9uZTsKICAgICAgICB9CgogICAgICAgICY6aG92ZXIgewogICAgICAgICAgYmFja2dyb3VuZDogI2YwZjhmZjsKICAgICAgICAgIGNvbG9yOiAjMTg5MGZmOwogICAgICAgIH0KICAgICAgfQogICAgfQogIH0KfQoKLnNlYXJjaC1mb3JtIHsKICAuZm9ybS1yb3cgewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBnYXA6IDE2cHg7CiAgICBmbGV4LXdyYXA6IHdyYXA7CiAgfQoKICAuZm9ybS1ncm91cCB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGdhcDogNnB4OwoKICAgIC5mb3JtLWxhYmVsIHsKICAgICAgZm9udC1zaXplOiAxM3B4OwogICAgICBjb2xvcjogIzU5NTk1OTsKICAgICAgZm9udC13ZWlnaHQ6IDQwMDsKICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKICAgICAgbWFyZ2luOiAwOwogICAgfQoKICAgIC5mb3JtLWlucHV0IHsKICAgICAgaGVpZ2h0OiAyOHB4OwogICAgICBwYWRkaW5nOiAwIDhweDsKICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsKICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICBmb250LXNpemU6IDEzcHg7CiAgICAgIGNvbG9yOiAjMjYyNjI2OwogICAgICBiYWNrZ3JvdW5kOiAjZmZmZmZmOwogICAgICBvdXRsaW5lOiBub25lOwogICAgICB0cmFuc2l0aW9uOiBib3JkZXItY29sb3IgMC4yczsKCiAgICAgICY6Zm9jdXMgewogICAgICAgIGJvcmRlci1jb2xvcjogIzE4OTBmZjsKICAgICAgICBib3gtc2hhZG93OiAwIDAgMCAycHggcmdiYSgyNCwgMTQ0LCAyNTUsIDAuMSk7CiAgICAgIH0KCiAgICAgICYua2V5d29yZC1pbnB1dCB7CiAgICAgICAgd2lkdGg6IDIwMHB4OwogICAgICB9CgogICAgICAmLnRpbWUtaW5wdXQgewogICAgICAgIHdpZHRoOiA2MHB4OwogICAgICB9CgogICAgICAmOjpwbGFjZWhvbGRlciB7CiAgICAgICAgY29sb3I6ICNiZmJmYmY7CiAgICAgICAgZm9udC1zaXplOiAxM3B4OwogICAgICB9CiAgICB9CgogICAgLmZvcm0tc2VsZWN0IHsKICAgICAgaGVpZ2h0OiAyOHB4OwogICAgICBwYWRkaW5nOiAwIDIwcHggMCA4cHg7CiAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7CiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgZm9udC1zaXplOiAxM3B4OwogICAgICBjb2xvcjogIzI2MjYyNjsKICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZiB1cmwoImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM2NzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyBmaWxsPSdub25lJyB2aWV3Qm94PScwIDAgMjAgMjAnJTNlJTNjcGF0aCBzdHJva2U9JyUyMzZiNzI4MCcgc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJyBzdHJva2UtbGluZWpvaW49J3JvdW5kJyBzdHJva2Utd2lkdGg9JzEuNScgZD0nbTYgOCA0IDQgNC00Jy8lM2UlM2Mvc3ZnJTNlIikgbm8tcmVwZWF0IHJpZ2h0IDZweCBjZW50ZXIvMTJweCAxMnB4OwogICAgICBvdXRsaW5lOiBub25lOwogICAgICBhcHBlYXJhbmNlOiBub25lOwogICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgIG1pbi13aWR0aDogODBweDsKICAgICAgdHJhbnNpdGlvbjogYm9yZGVyLWNvbG9yIDAuMnM7CgogICAgICAmOmZvY3VzIHsKICAgICAgICBib3JkZXItY29sb3I6ICMxODkwZmY7CiAgICAgICAgYm94LXNoYWRvdzogMCAwIDAgMnB4IHJnYmEoMjQsIDE0NCwgMjU1LCAwLjEpOwogICAgICB9CiAgICB9CiAgfQoKICAuZm9ybS1idXR0b25zIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgZ2FwOiA4cHg7CiAgICBtYXJnaW4tbGVmdDogYXV0bzsKCiAgICAuYnRuIHsKICAgICAgaGVpZ2h0OiAyOHB4OwogICAgICBwYWRkaW5nOiAwIDEycHg7CiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgZm9udC1zaXplOiAxM3B4OwogICAgICBmb250LXdlaWdodDogNDAwOwogICAgICBib3JkZXI6IDFweCBzb2xpZDsKICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICBvdXRsaW5lOiBub25lOwogICAgICB0cmFuc2l0aW9uOiBhbGwgMC4yczsKICAgICAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKCiAgICAgICYuYnRuLXByaW1hcnkgewogICAgICAgIGJhY2tncm91bmQ6ICMxODkwZmY7CiAgICAgICAgYm9yZGVyLWNvbG9yOiAjMTg5MGZmOwogICAgICAgIGNvbG9yOiAjZmZmZmZmOwoKICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgIGJhY2tncm91bmQ6ICM0MGE5ZmY7CiAgICAgICAgICBib3JkZXItY29sb3I6ICM0MGE5ZmY7CiAgICAgICAgfQogICAgICB9CgogICAgICAmLmJ0bi1kZWZhdWx0IHsKICAgICAgICBiYWNrZ3JvdW5kOiAjZmZmZmZmOwogICAgICAgIGJvcmRlci1jb2xvcjogI2Q5ZDlkOTsKICAgICAgICBjb2xvcjogIzU5NTk1OTsKCiAgICAgICAgJjpob3ZlciB7CiAgICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgICAgIGJvcmRlci1jb2xvcjogIzE4OTBmZjsKICAgICAgICB9CiAgICAgIH0KCiAgICAgICYuYnRuLXN1Y2Nlc3MgewogICAgICAgIGJhY2tncm91bmQ6ICM1MmM0MWE7CiAgICAgICAgYm9yZGVyLWNvbG9yOiAjNTJjNDFhOwogICAgICAgIGNvbG9yOiAjZmZmZmZmOwoKICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgIGJhY2tncm91bmQ6ICM3M2QxM2Q7CiAgICAgICAgICBib3JkZXItY29sb3I6ICM3M2QxM2Q7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQp9CgovLyDpobbpg6jljLrln5/vvJrpl6jlupfokKXkuJrpop3liY3ljYHnmoQgKyDmmbrog73liqnmiYsKLnRvcC1zZWN0aW9uIHsKICBkaXNwbGF5OiBmbGV4OwogIGdhcDogMjBweDsKICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsKfQoKLy8g5Lit6Ze05Yy65Z+f77ya6JCl5Lia6aKd5ZCM5q+UIOWNleeLrOS4gOihjAoubWlkZGxlLXNlY3Rpb24gewogIHdpZHRoOiAxMDAlOwp9CgovLyDlupXpg6jljLrln5/vvJrlk4HniYzpl6jlupfokKXkuJrpop3liY3ljYHnmoQgKyDmmbrog73liqnmiYsKLmJvdHRvbS1zZWN0aW9uIHsKICBkaXNwbGF5OiBmbGV4OwogIGdhcDogMjBweDsKICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsKfQoKLy8g5Li75Zu+6KGo5Yy65Z+fCi5tYWluLWNoYXJ0IHsKICBmbGV4OiAxOwogIG1pbi13aWR0aDogMDsKfQoKLy8g5pm66IO95Yqp5omL6Z2i5p2/Ci5hc3Npc3RhbnQtcGFuZWwgewogIHdpZHRoOiAyODBweDsKICBiYWNrZ3JvdW5kOiAjZmZmZmZmOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOwogIG92ZXJmbG93OiBoaWRkZW47CiAgZmxleC1zaHJpbms6IDA7CgogIC5wYW5lbC1oZWFkZXIgewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICBwYWRkaW5nOiAxNnB4IDIwcHg7CiAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsKICAgIGJhY2tncm91bmQ6ICNmYWZiZmM7CgogICAgc3BhbiB7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgZm9udC13ZWlnaHQ6IDUwMDsKICAgICAgY29sb3I6ICMyNjI2MjY7CiAgICB9CgogICAgLnBhbmVsLWNsb3NlIHsKICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICBmb250LXNpemU6IDE2cHg7CiAgICAgIGNvbG9yOiAjOGM4YzhjOwogICAgfQogIH0KCiAgLnBhbmVsLWNvbnRlbnQgewogICAgcGFkZGluZzogMjBweDsKCiAgICAuYXNzaXN0YW50LWl0ZW0gewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBnYXA6IDEycHg7CiAgICAgIHBhZGRpbmc6IDEycHggMDsKICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7CgogICAgICAmOmxhc3QtY2hpbGQgewogICAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7CiAgICAgIH0KCiAgICAgIC5hc3Npc3RhbnQtaWNvbiB7CiAgICAgICAgZm9udC1zaXplOiAyMHB4OwogICAgICAgIHdpZHRoOiAzMnB4OwogICAgICAgIGhlaWdodDogMzJweDsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgYmFja2dyb3VuZDogI2Y1ZjVmNTsKICAgICAgICBib3JkZXItcmFkaXVzOiA2cHg7CiAgICAgIH0KCiAgICAgIC5hc3Npc3RhbnQtdGV4dCB7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIGNvbG9yOiAjMjYyNjI2OwogICAgICAgIGxpbmUtaGVpZ2h0OiAxLjQ7CiAgICAgICAgZmxleDogMTsKICAgICAgfQogICAgfQogIH0KfQoKLmNoYXJ0LWNhcmQsIC52YWx1ZS1jYXJkIHsKICBiYWNrZ3JvdW5kOiAjZmZmZmZmOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5jaGFydC1oZWFkZXIsIC52YWx1ZS1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgcGFkZGluZzogMTZweCAyMHB4OwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOwogIGJhY2tncm91bmQ6ICNmYWZiZmM7Cn0KCi5jaGFydC10aXRsZSwgLnZhbHVlLXRpdGxlIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZ2FwOiA4cHg7CiAgZm9udC1zaXplOiAxNHB4OwogIGZvbnQtd2VpZ2h0OiA1MDA7CiAgY29sb3I6ICMyNjI2MjY7CgogIC5jaGFydC1pY29uIHsKICAgIHdpZHRoOiAxNnB4OwogICAgaGVpZ2h0OiAxNnB4OwogICAgYmFja2dyb3VuZDogIzE4OTBmZjsKICAgIGJvcmRlci1yYWRpdXM6IDJweDsKICB9CgogIC5oZWxwLWljb24gewogICAgd2lkdGg6IDE2cHg7CiAgICBoZWlnaHQ6IDE2cHg7CiAgICBiYWNrZ3JvdW5kOiAjZDlkOWQ5OwogICAgYm9yZGVyLXJhZGl1czogNTAlOwogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICAgIGZvbnQtc2l6ZTogMTJweDsKICAgIGNvbG9yOiAjZmZmZmZmOwogICAgY3Vyc29yOiBwb2ludGVyOwogIH0KfQoKLmNoYXJ0LW1ldGEsIC52YWx1ZS1tZXRhIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZ2FwOiAxMnB4OwogIGZvbnQtc2l6ZTogMTJweDsKICBjb2xvcjogIzhjOGM4YzsKCiAgLmNoYXJ0LWRhdGUsIC52YWx1ZS1kYXRlIHsKICAgIGNvbG9yOiAjNTk1OTU5OwogIH0KCiAgLmNoYXJ0LXR5cGUsIC52YWx1ZS10eXBlIHsKICAgIGJhY2tncm91bmQ6ICNmMGYwZjA7CiAgICBwYWRkaW5nOiAycHggNnB4OwogICAgYm9yZGVyLXJhZGl1czogMnB4OwogIH0KCiAgLmNoYXJ0LXNvdXJjZSB7CiAgICBjb2xvcjogIzE4OTBmZjsKICB9Cn0KCi5jaGFydC1hY3Rpb25zLCAudmFsdWUtYWN0aW9ucyB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogOHB4OwoKICAuYWN0aW9uLWljb24gewogICAgd2lkdGg6IDE2cHg7CiAgICBoZWlnaHQ6IDE2cHg7CiAgICBib3JkZXItcmFkaXVzOiAycHg7CiAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICBiYWNrZ3JvdW5kLXNpemU6IDEycHggMTJweDsKICAgIGJhY2tncm91bmQtcG9zaXRpb246IGNlbnRlcjsKICAgIGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7CiAgICB0cmFuc2l0aW9uOiBhbGwgMC4yczsKCiAgICAmLnJlZnJlc2ggewogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNTJjNDFhOwogICAgICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM2NzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyBmaWxsPSdub25lJyB2aWV3Qm94PScwIDAgMjQgMjQnIHN0cm9rZT0nd2hpdGUnIHN0cm9rZS13aWR0aD0nMiclM2UlM2NwYXRoIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcgc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcgZD0nTTQgNHY1aC41ODJtMTUuMzU2IDJBOC4wMDEgOC4wMDEgMCAwMDQuNTgyIDltMCAwSDltMTEgMTF2LTVoLS41ODFtMCAwYTguMDAzIDguMDAzIDAgMDEtMTUuMzU3LTJtMTUuMzU3IDJIMTUnLyUzZSUzYy9zdmclM2UiKTsKCiAgICAgICY6aG92ZXIgewogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM3M2QxM2Q7CiAgICAgIH0KICAgIH0KCiAgICAmLmRvd25sb2FkIHsKICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzE4OTBmZjsKICAgICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKCJkYXRhOmltYWdlL3N2Zyt4bWwsJTNjc3ZnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZycgZmlsbD0nbm9uZScgdmlld0JveD0nMCAwIDI0IDI0JyBzdHJva2U9J3doaXRlJyBzdHJva2Utd2lkdGg9JzInJTNlJTNjcGF0aCBzdHJva2UtbGluZWNhcD0ncm91bmQnIHN0cm9rZS1saW5lam9pbj0ncm91bmQnIGQ9J00xMiAxMHY2bTAgMGwtMy0zbTMgM2wzLTNtMiA4SDdhMiAyIDAgMDEtMi0yVjVhMiAyIDAgMDEyLTJoNS41ODZhMSAxIDAgMDEuNzA3LjI5M2w1LjQxNCA1LjQxNGExIDEgMCAwMS4yOTMuNzA3VjE5YTIgMiAwIDAxLTIgMnonLyUzZSUzYy9zdmclM2UiKTsKCiAgICAgICY6aG92ZXIgewogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM0MGE5ZmY7CiAgICAgIH0KICAgIH0KCiAgICAmLm1vcmUgewogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjOGM4YzhjOwogICAgICBiYWNrZ3JvdW5kLWltYWdlOiB1cmwoImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM2NzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyBmaWxsPSdub25lJyB2aWV3Qm94PScwIDAgMjQgMjQnIHN0cm9rZT0nd2hpdGUnIHN0cm9rZS13aWR0aD0nMiclM2UlM2NwYXRoIHN0cm9rZS1saW5lY2FwPSdyb3VuZCcgc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcgZD0nTTEyIDV2LjAxTTEyIDEydi4wMU0xMiAxOXYuMDFNMTIgNmExIDEgMCAxMTAtMiAxIDEgMCAwMTAgMnptMCA3YTEgMSAwIDExMC0yIDEgMSAwIDAxMCAyem0wIDdhMSAxIDAgMTEwLTIgMSAxIDAgMDEwIDJ6Jy8lM2UlM2Mvc3ZnJTNlIik7CgogICAgICAmOmhvdmVyIHsKICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjYTZhNmE2OwogICAgICB9CiAgICB9CgogICAgJi5zZXR0aW5ncyB7CiAgICAgIGJhY2tncm91bmQtY29sb3I6ICM3MjJlZDE7CiAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiZGF0YTppbWFnZS9zdmcreG1sLCUzY3N2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAyNCAyNCcgZmlsbD0nd2hpdGUnJTNlJTNjcmVjdCB4PSczJyB5PScxMicgd2lkdGg9JzQnIGhlaWdodD0nOScvJTNlJTNjcmVjdCB4PScxMCcgeT0nOCcgd2lkdGg9JzQnIGhlaWdodD0nMTMnLyUzZSUzY3JlY3QgeD0nMTcnIHk9JzQnIHdpZHRoPSc0JyBoZWlnaHQ9JzE3Jy8lM2UlM2Mvc3ZnJTNlIik7CgogICAgICAmOmhvdmVyIHsKICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjOTI1NGRlOwogICAgICB9CiAgICB9CgogICAgJi5jbG9zZSB7CiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZjRkNGY7CiAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgiZGF0YTppbWFnZS9zdmcreG1sLCUzY3N2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIGZpbGw9J25vbmUnIHZpZXdCb3g9JzAgMCAyNCAyNCcgc3Ryb2tlPSd3aGl0ZScgc3Ryb2tlLXdpZHRoPScyJyUzZSUzY3BhdGggc3Ryb2tlLWxpbmVjYXA9J3JvdW5kJyBzdHJva2UtbGluZWpvaW49J3JvdW5kJyBkPSdNNiAxOEwxOCA2TTYgNmwxMiAxMicvJTNlJTNjL3N2ZyUzZSIpOwoKICAgICAgJjpob3ZlciB7CiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2ZmNzg3NTsKICAgICAgfQogICAgfQogIH0KCiAgLmNoYXJ0LXN0YXR1cyB7CiAgICBmb250LXNpemU6IDEycHg7CiAgICBjb2xvcjogIzhjOGM4YzsKICAgIG1hcmdpbi1sZWZ0OiA4cHg7CiAgfQp9CgouY2hhcnQtY29udGVudCB7CiAgcGFkZGluZzogMjBweDsKfQoKLmNoYXJ0LWxlZ2VuZCB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogMjBweDsKICBtYXJnaW4tYm90dG9tOiAxNnB4OwogIGZvbnQtc2l6ZTogMTJweDsKCiAgLmxlZ2VuZC1pdGVtIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgZ2FwOiA2cHg7CgogICAgLmxlZ2VuZC1jb2xvciB7CiAgICAgIHdpZHRoOiAxMnB4OwogICAgICBoZWlnaHQ6IDEycHg7CiAgICAgIGJvcmRlci1yYWRpdXM6IDJweDsKCiAgICAgICYuYmx1ZSB7CiAgICAgICAgYmFja2dyb3VuZDogIzVCOEZGOTsKICAgICAgfQoKICAgICAgJi55ZWxsb3cgewogICAgICAgIGJhY2tncm91bmQ6ICNGRkQ2NjY7CiAgICAgIH0KCiAgICAgICYubGluZSB7CiAgICAgICAgYmFja2dyb3VuZDogI0ZGNkI2QjsKICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7CiAgICAgICAgd2lkdGg6IDhweDsKICAgICAgICBoZWlnaHQ6IDhweDsKICAgICAgfQogICAgfQogIH0KfQoKLmNoYXJ0LXdyYXBwZXIgewogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogMzAwcHg7Cn0KCi5jaGFydCB7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwp9CgoudmFsdWUtY29udGVudCB7CiAgcGFkZGluZzogMjBweDsKfQoKLnZhbHVlLW1haW4gewogIC52YWx1ZS1sYWJlbCB7CiAgICBmb250LXNpemU6IDEycHg7CiAgICBjb2xvcjogIzhjOGM4YzsKICAgIG1hcmdpbi1ib3R0b206IDhweDsKICAgIGRpc3BsYXk6IGJsb2NrOwogIH0KCiAgLnZhbHVlLW51bWJlciB7CiAgICBmb250LXNpemU6IDM2cHg7CiAgICBmb250LXdlaWdodDogYm9sZDsKICAgIGNvbG9yOiAjMjYyNjI2OwogICAgbGluZS1oZWlnaHQ6IDE7CiAgICBtYXJnaW4tYm90dG9tOiAxMnB4OwoKICAgIC52YWx1ZS11bml0IHsKICAgICAgZm9udC1zaXplOiAxOHB4OwogICAgICBjb2xvcjogIzhjOGM4YzsKICAgICAgbWFyZ2luLWxlZnQ6IDRweDsKICAgIH0KICB9CgogIC52YWx1ZS1jaGFuZ2UgewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBnYXA6IDhweDsKICAgIGZvbnQtc2l6ZTogMTJweDsKCiAgICAuY2hhbmdlLXRleHQgewogICAgICBjb2xvcjogIzhjOGM4YzsKICAgIH0KCiAgICAuY2hhbmdlLXZhbHVlIHsKICAgICAgJi5wb3NpdGl2ZSB7CiAgICAgICAgY29sb3I6ICM1MmM0MWE7CiAgICAgIH0KCiAgICAgICYubmVnYXRpdmUgewogICAgICAgIGNvbG9yOiAjZmY0ZDRmOwogICAgICB9CiAgICB9CgogICAgLmNoYW5nZS1hcnJvdyB7CiAgICAgIHdpZHRoOiAwOwogICAgICBoZWlnaHQ6IDA7CgogICAgICAmLnVwIHsKICAgICAgICBib3JkZXItbGVmdDogNHB4IHNvbGlkIHRyYW5zcGFyZW50OwogICAgICAgIGJvcmRlci1yaWdodDogNHB4IHNvbGlkIHRyYW5zcGFyZW50OwogICAgICAgIGJvcmRlci1ib3R0b206IDZweCBzb2xpZCAjNTJjNDFhOwogICAgICB9CgogICAgICAmLmRvd24gewogICAgICAgIGJvcmRlci1sZWZ0OiA0cHggc29saWQgdHJhbnNwYXJlbnQ7CiAgICAgICAgYm9yZGVyLXJpZ2h0OiA0cHggc29saWQgdHJhbnNwYXJlbnQ7CiAgICAgICAgYm9yZGVyLXRvcDogNnB4IHNvbGlkICNmZjRkNGY7CiAgICAgIH0KICAgIH0KICB9Cn0KCi5jb250cm9sLXBhbmVsIHsKICBwb3NpdGlvbjogZml4ZWQ7CiAgcmlnaHQ6IDIwcHg7CiAgdG9wOiA1MCU7CiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpOwogIHdpZHRoOiAyODBweDsKICBiYWNrZ3JvdW5kOiAjZmZmZmZmOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICBib3gtc2hhZG93OiAwIDRweCAxNnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7CiAgei1pbmRleDogMTAwMDsKCiAgLnBhbmVsLWhlYWRlciB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIHBhZGRpbmc6IDE2cHggMjBweDsKICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOwogICAgYmFja2dyb3VuZDogI2ZhZmJmYzsKICAgIGJvcmRlci1yYWRpdXM6IDhweCA4cHggMCAwOwoKICAgIHNwYW4gewogICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7CiAgICAgIGNvbG9yOiAjMjYyNjI2OwogICAgfQoKICAgIC5wYW5lbC1jbG9zZSB7CiAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICBjb2xvcjogIzhjOGM4YzsKICAgIH0KICB9CgogIC5wYW5lbC1jb250ZW50IHsKICAgIHBhZGRpbmc6IDIwcHg7CgogICAgLnBhbmVsLXNlY3Rpb24gewogICAgICBoNCB7CiAgICAgICAgbWFyZ2luOiAwIDAgMTJweCAwOwogICAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgICBjb2xvcjogIzI2MjYyNjsKICAgICAgfQoKICAgICAgLnNldHRpbmctaXRlbSB7CiAgICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDsKCiAgICAgICAgbGFiZWwgewogICAgICAgICAgZGlzcGxheTogYmxvY2s7CiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA2cHg7CiAgICAgICAgICBmb250LXNpemU6IDEycHg7CiAgICAgICAgICBjb2xvcjogIzhjOGM4YzsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0KCi8vIOWTjeW6lOW8j+iuvuiuoQpAbWVkaWEgKG1heC13aWR0aDogMTIwMHB4KSB7CiAgLnNlYXJjaC1jb250YWluZXIgewogICAgLnNlYXJjaC1mb3JtIHsKICAgICAgLmZvcm0tcm93IHsKICAgICAgICBnYXA6IDEycHg7CiAgICAgIH0KCiAgICAgIC5mb3JtLWdyb3VwIHsKICAgICAgICAuZm9ybS1pbnB1dC5rZXl3b3JkLWlucHV0IHsKICAgICAgICAgIHdpZHRoOiAxNjBweDsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5mb3JtLWJ1dHRvbnMgewogICAgICAgIG1hcmdpbi1sZWZ0OiAwOwogICAgICAgIG1hcmdpbi10b3A6IDhweDsKICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGZsZXgtc3RhcnQ7CiAgICAgIH0KICAgIH0KICB9CgogIC50b3Atc2VjdGlvbiwgLmJvdHRvbS1zZWN0aW9uIHsKICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICBnYXA6IDE1cHg7CiAgfQoKICAuYXNzaXN0YW50LXBhbmVsIHsKICAgIHdpZHRoOiAxMDAlOwogICAgb3JkZXI6IC0xOyAvLyDmmbrog73liqnmiYvpnaLmnb/lnKjnp7vliqjnq6/mmL7npLrlnKjlm77ooajkuIrmlrkKICB9Cn0KCkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkgewogIC5kYXNoYm9hcmQtY29udGFpbmVyIHsKICAgIHBhZGRpbmc6IDEwcHg7CiAgICBnYXA6IDE1cHg7CiAgfQoKICAuc2VhcmNoLWNvbnRhaW5lciB7CiAgICAuc2VhcmNoLWZvcm0gewogICAgICAuZm9ybS1yb3cgewogICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICAgICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7CiAgICAgICAgZ2FwOiAxMnB4OwogICAgICB9CgogICAgICAuZm9ybS1ncm91cCB7CiAgICAgICAgd2lkdGg6IDEwMCU7CgogICAgICAgIC5mb3JtLWlucHV0IHsKICAgICAgICAgIGZsZXg6IDE7CiAgICAgICAgICBtaW4td2lkdGg6IDEyMHB4OwoKICAgICAgICAgICYua2V5d29yZC1pbnB1dCB7CiAgICAgICAgICAgIHdpZHRoOiAxMDAlOwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgLmZvcm0tc2VsZWN0IHsKICAgICAgICAgIGZsZXg6IDE7CiAgICAgICAgICBtaW4td2lkdGg6IDEyMHB4OwogICAgICAgIH0KICAgICAgfQoKICAgICAgLmZvcm0tYnV0dG9ucyB7CiAgICAgICAgd2lkdGg6IDEwMCU7CiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgbWFyZ2luLXRvcDogMTJweDsKICAgICAgfQogICAgfQogIH0KCiAgLnRvcC1zZWN0aW9uLCAuYm90dG9tLXNlY3Rpb24gewogICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICAgIGdhcDogMTBweDsKICB9CgogIC5hc3Npc3RhbnQtcGFuZWwgewogICAgd2lkdGg6IDEwMCU7CiAgICBvcmRlcjogLTE7CgogICAgLnBhbmVsLWNvbnRlbnQgewogICAgICBwYWRkaW5nOiAxNXB4OwoKICAgICAgLmFzc2lzdGFudC1pdGVtIHsKICAgICAgICBwYWRkaW5nOiAxMHB4IDA7CgogICAgICAgIC5hc3Npc3RhbnQtaWNvbiB7CiAgICAgICAgICB3aWR0aDogMjhweDsKICAgICAgICAgIGhlaWdodDogMjhweDsKICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsKICAgICAgICB9CgogICAgICAgIC5hc3Npc3RhbnQtdGV4dCB7CiAgICAgICAgICBmb250LXNpemU6IDEzcHg7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQoKICAuY2hhcnQtaGVhZGVyLCAudmFsdWUtaGVhZGVyIHsKICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgICBhbGlnbi1pdGVtczogZmxleC1zdGFydDsKICAgIGdhcDogOHB4OwogICAgcGFkZGluZzogMTJweCAxNnB4OwogIH0KCiAgLmNoYXJ0LW1ldGEsIC52YWx1ZS1tZXRhIHsKICAgIG9yZGVyOiAxOwogIH0KCiAgLmNoYXJ0LWFjdGlvbnMsIC52YWx1ZS1hY3Rpb25zIHsKICAgIG9yZGVyOiAyOwogICAgYWxpZ24tc2VsZjogZmxleC1lbmQ7CiAgfQoKICAuY2hhcnQtY29udGVudCB7CiAgICBwYWRkaW5nOiAxNnB4OwogIH0KCiAgLnZhbHVlLWNvbnRlbnQgewogICAgcGFkZGluZzogMTZweDsKICB9CgogIC5jaGFydC13cmFwcGVyIHsKICAgIGhlaWdodDogMjUwcHg7CiAgfQoKICAudmFsdWUtbnVtYmVyIHsKICAgIGZvbnQtc2l6ZTogMjhweCAhaW1wb3J0YW50OwoKICAgIC52YWx1ZS11bml0IHsKICAgICAgZm9udC1zaXplOiAxNHB4ICFpbXBvcnRhbnQ7CiAgICB9CiAgfQp9CgovLyDmm7TlpJrmk43kvZzlvLnnqpfmoLflvI8KLm1vcmUtcG9wdXAgewogIGJhY2tncm91bmQ6ICNmZmZmZmY7CiAgYm9yZGVyOiAxcHggc29saWQgI2U4ZThlODsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpOwogIG1pbi13aWR0aDogMTYwcHg7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICBwb3NpdGlvbjogZml4ZWQ7CiAgei1pbmRleDogMjAwMDsKCiAgLm1vcmUtcG9wdXAtY29udGVudCB7CiAgICAubW9yZS1hY3Rpb24taXRlbSB7CiAgICAgIHBhZGRpbmc6IDEycHggMTZweDsKICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICBjb2xvcjogIzI2MjYyNjsKICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICB0cmFuc2l0aW9uOiBhbGwgMC4yczsKICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmNWY1ZjU7CiAgICAgIGxpbmUtaGVpZ2h0OiAxLjQ7CgogICAgICAmOmxhc3QtY2hpbGQgewogICAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7CiAgICAgIH0KCiAgICAgICY6aG92ZXIgewogICAgICAgIGJhY2tncm91bmQ6ICNmMGY4ZmY7CiAgICAgICAgY29sb3I6ICMxODkwZmY7CiAgICAgIH0KCiAgICAgICY6YWN0aXZlIHsKICAgICAgICBiYWNrZ3JvdW5kOiAjZTZmN2ZmOwogICAgICAgIGNvbG9yOiAjMTg5MGZmOwogICAgICB9CiAgICB9CiAgfQp9CgovLyDliIbkuqvlvLnnqpfmoLflvI8KLnNoYXJlLXBvcHVwLW92ZXJsYXkgewogIHBvc2l0aW9uOiBmaXhlZDsKICB0b3A6IDA7CiAgbGVmdDogMDsKICByaWdodDogMDsKICBib3R0b206IDA7CiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjUpOwogIHotaW5kZXg6IDk5OTk7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwp9Cgouc2hhcmUtcG9wdXAgewogIGJhY2tncm91bmQ6IHdoaXRlOwogIGJvcmRlci1yYWRpdXM6IDhweDsKICB3aWR0aDogNDAwcHg7CiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMTUpOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi5zaGFyZS1wb3B1cC1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgcGFkZGluZzogMTZweCAyMHB4OwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZjBmMGYwOwogIGJhY2tncm91bmQ6ICNmYWZhZmE7Cn0KCi5zaGFyZS1wb3B1cC10aXRsZSB7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiA1MDA7CiAgY29sb3I6ICMzMzM7Cn0KCi5zaGFyZS1wb3B1cC1jbG9zZSB7CiAgYmFja2dyb3VuZDogbm9uZTsKICBib3JkZXI6IG5vbmU7CiAgZm9udC1zaXplOiAyMHB4OwogIGNvbG9yOiAjOTk5OwogIGN1cnNvcjogcG9pbnRlcjsKICBwYWRkaW5nOiAwOwogIHdpZHRoOiAyNHB4OwogIGhlaWdodDogMjRweDsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CgogICY6aG92ZXIgewogICAgY29sb3I6ICM2NjY7CiAgfQp9Cgouc2hhcmUtcG9wdXAtY29udGVudCB7CiAgcGFkZGluZzogMjBweDsKfQoKLnNoYXJlLWRlc2NyaXB0aW9uIHsKICBjb2xvcjogIzY2NjsKICBmb250LXNpemU6IDE0cHg7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKICBsaW5lLWhlaWdodDogMS41Owp9Cgouc2hhcmUtb3B0aW9uIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDIwcHg7Cn0KCi5zaGFyZS1vcHRpb24tbGFiZWwgewogIGZvbnQtc2l6ZTogMTRweDsKICBjb2xvcjogIzMzMzsKfQoKLnNoYXJlLXRvZ2dsZSB7CiAgcG9zaXRpb246IHJlbGF0aXZlOwp9CgoudG9nZ2xlLWlucHV0IHsKICBkaXNwbGF5OiBub25lOwp9CgoudG9nZ2xlLWxhYmVsIHsKICBkaXNwbGF5OiBibG9jazsKICB3aWR0aDogNDRweDsKICBoZWlnaHQ6IDI0cHg7CiAgYmFja2dyb3VuZDogI2RkZDsKICBib3JkZXItcmFkaXVzOiAxMnB4OwogIGN1cnNvcjogcG9pbnRlcjsKICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZCAwLjNzOwoKICAmOjphZnRlciB7CiAgICBjb250ZW50OiAnJzsKICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgIHRvcDogMnB4OwogICAgbGVmdDogMnB4OwogICAgd2lkdGg6IDIwcHg7CiAgICBoZWlnaHQ6IDIwcHg7CiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICAgIGJvcmRlci1yYWRpdXM6IDUwJTsKICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzOwogIH0KfQoKLnRvZ2dsZS1pbnB1dDpjaGVja2VkICsgLnRvZ2dsZS1sYWJlbCB7CiAgYmFja2dyb3VuZDogIzE4OTBmZjsKCiAgJjo6YWZ0ZXIgewogICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVYKDIwcHgpOwogIH0KfQoKLnNoYXJlLWxpbmstc2VjdGlvbiB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDhweDsKfQoKLnNoYXJlLWxpbmstaW5wdXQgewogIGZsZXg6IDE7CiAgaGVpZ2h0OiAzNnB4OwogIHBhZGRpbmc6IDAgMTJweDsKICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBmb250LXNpemU6IDE0cHg7CiAgY29sb3I6ICM2NjY7CiAgYmFja2dyb3VuZDogI2Y1ZjVmNTsKICBvdXRsaW5lOiBub25lOwp9CgouY29weS1saW5rLWJ0biB7CiAgaGVpZ2h0OiAzNnB4OwogIHBhZGRpbmc6IDAgMTZweDsKICBiYWNrZ3JvdW5kOiAjMTg5MGZmOwogIGNvbG9yOiB3aGl0ZTsKICBib3JkZXI6IG5vbmU7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGZvbnQtc2l6ZTogMTRweDsKICBjdXJzb3I6IHBvaW50ZXI7CiAgdHJhbnNpdGlvbjogYmFja2dyb3VuZCAwLjNzOwoKICAmOmhvdmVyIHsKICAgIGJhY2tncm91bmQ6ICM0MGE5ZmY7CiAgfQoKICAmOmFjdGl2ZSB7CiAgICBiYWNrZ3JvdW5kOiAjMDk2ZGQ5OwogIH0KfQoKLy8g5Y2h54mH5o+Q6YaS5by556qX5qC35byPCi5yZW1pbmRlci1wb3B1cC1vdmVybGF5IHsKICBwb3NpdGlvbjogZml4ZWQ7CiAgdG9wOiAwOwogIGxlZnQ6IDA7CiAgcmlnaHQ6IDA7CiAgYm90dG9tOiAwOwogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC41KTsKICB6LWluZGV4OiAxMDAwMCAhaW1wb3J0YW50OwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKfQoKLnJlbWluZGVyLXBvcHVwIHsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBib3JkZXItcmFkaXVzOiA4cHg7CiAgd2lkdGg6IDUyMHB4OwogIG1heC13aWR0aDogOTB2dzsKICBib3gtc2hhZG93OiAwIDRweCAyMHB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7CiAgb3ZlcmZsb3c6IGhpZGRlbjsKICB6LWluZGV4OiAxMDAwMSAhaW1wb3J0YW50OwogIHBvc2l0aW9uOiByZWxhdGl2ZTsKfQoKLnJlbWluZGVyLXBvcHVwLWhlYWRlciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBwYWRkaW5nOiAxNnB4IDIwcHg7CiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNmMGYwZjA7CiAgYmFja2dyb3VuZDogI2ZhZmFmYTsKfQoKLnJlbWluZGVyLXBvcHVwLXRpdGxlIHsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICBjb2xvcjogIzMzMzsKfQoKLnJlbWluZGVyLXBvcHVwLWNsb3NlIHsKICBiYWNrZ3JvdW5kOiBub25lOwogIGJvcmRlcjogbm9uZTsKICBmb250LXNpemU6IDIwcHg7CiAgY29sb3I6ICM5OTk7CiAgY3Vyc29yOiBwb2ludGVyOwogIHBhZGRpbmc6IDA7CiAgd2lkdGg6IDI0cHg7CiAgaGVpZ2h0OiAyNHB4OwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKCiAgJjpob3ZlciB7CiAgICBjb2xvcjogIzY2NjsKICB9Cn0KCi5yZW1pbmRlci1wb3B1cC1jb250ZW50IHsKICBwYWRkaW5nOiAyMHB4Owp9CgoucmVtaW5kZXItZm9ybS1pdGVtIHsKICBtYXJnaW4tYm90dG9tOiAxNnB4OwoKICAucmVtaW5kZXItbGFiZWwgewogICAgZGlzcGxheTogYmxvY2s7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBjb2xvcjogIzMzMzsKICAgIG1hcmdpbi1ib3R0b206IDhweDsKICAgIGZvbnQtd2VpZ2h0OiA1MDA7CiAgfQoKICAucmVtaW5kZXItc2VsZWN0IHsKICAgIHdpZHRoOiAxMDAlOwogICAgaGVpZ2h0OiAzNnB4OwogICAgcGFkZGluZzogMCAxMnB4OwogICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsKICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIGNvbG9yOiAjMzMzOwogICAgYmFja2dyb3VuZDogd2hpdGU7CiAgICBvdXRsaW5lOiBub25lOwogICAgY3Vyc29yOiBwb2ludGVyOwoKICAgICY6Zm9jdXMgewogICAgICBib3JkZXItY29sb3I6ICMxODkwZmY7CiAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDI0LCAxNDQsIDI1NSwgMC4yKTsKICAgIH0KICB9CgogIC5yZW1pbmRlci1pbnB1dCB7CiAgICB3aWR0aDogMTAwJTsKICAgIGhlaWdodDogMzZweDsKICAgIHBhZGRpbmc6IDAgMTJweDsKICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7CiAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBjb2xvcjogIzMzMzsKICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgb3V0bGluZTogbm9uZTsKCiAgICAmOmZvY3VzIHsKICAgICAgYm9yZGVyLWNvbG9yOiAjMTg5MGZmOwogICAgICBib3gtc2hhZG93OiAwIDAgMCAycHggcmdiYSgyNCwgMTQ0LCAyNTUsIDAuMik7CiAgICB9CgogICAgJjo6cGxhY2Vob2xkZXIgewogICAgICBjb2xvcjogI2JmYmZiZjsKICAgIH0KICB9CgogIC5yZW1pbmRlci1jaGFuZ2Utc2VjdGlvbiB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAgZ2FwOiAxMnB4OwoKICAgIC5yZW1pbmRlci1zZWxlY3Qtc21hbGwgewogICAgICBmbGV4OiAxOwogICAgICBoZWlnaHQ6IDM2cHg7CiAgICAgIHBhZGRpbmc6IDAgMTJweDsKICAgICAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsKICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgIGNvbG9yOiAjMzMzOwogICAgICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICAgICAgb3V0bGluZTogbm9uZTsKICAgICAgY3Vyc29yOiBwb2ludGVyOwoKICAgICAgJjpmb2N1cyB7CiAgICAgICAgYm9yZGVyLWNvbG9yOiAjMTg5MGZmOwogICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDI0LCAxNDQsIDI1NSwgMC4yKTsKICAgICAgfQogICAgfQogIH0KCiAgLnJlbWluZGVyLXRocmVzaG9sZC1zZWN0aW9uIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgZ2FwOiAxMnB4OwoKICAgIC5yZW1pbmRlci1udW1iZXItaW5wdXQgewogICAgICB3aWR0aDogMTIwcHg7CiAgICAgIGhlaWdodDogMzZweDsKICAgICAgcGFkZGluZzogMCAxMnB4OwogICAgICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5OwogICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgY29sb3I6ICMzMzM7CiAgICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgICBvdXRsaW5lOiBub25lOwoKICAgICAgJjpmb2N1cyB7CiAgICAgICAgYm9yZGVyLWNvbG9yOiAjMTg5MGZmOwogICAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDI0LCAxNDQsIDI1NSwgMC4yKTsKICAgICAgfQogICAgfQoKICAgIC5yZW1pbmRlci11bml0IHsKICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICBjb2xvcjogIzY2NjsKICAgIH0KCiAgICAucmVtaW5kZXItY2hlY2tib3gtc2VjdGlvbiB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIGdhcDogNnB4OwogICAgICBtYXJnaW4tbGVmdDogYXV0bzsKCiAgICAgIC5yZW1pbmRlci1jaGVja2JveCB7CiAgICAgICAgd2lkdGg6IDE2cHg7CiAgICAgICAgaGVpZ2h0OiAxNnB4OwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgfQoKICAgICAgLnJlbWluZGVyLWNoZWNrYm94LWxhYmVsIHsKICAgICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgICAgY29sb3I6ICMzMzM7CiAgICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICAgIG1hcmdpbjogMDsKICAgICAgfQogICAgfQogIH0KCiAgLnJlbWluZGVyLW1ldGhvZC1zZWN0aW9uIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBnYXA6IDIwcHg7CgogICAgLnJlbWluZGVyLXJhZGlvLWl0ZW0gewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBnYXA6IDZweDsKCiAgICAgIC5yZW1pbmRlci1yYWRpbyB7CiAgICAgICAgd2lkdGg6IDE2cHg7CiAgICAgICAgaGVpZ2h0OiAxNnB4OwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgfQoKICAgICAgLnJlbWluZGVyLXJhZGlvLWxhYmVsIHsKICAgICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgICAgY29sb3I6ICMzMzM7CiAgICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICAgIG1hcmdpbjogMDsKICAgICAgfQogICAgfQogIH0KfQoKLnJlbWluZGVyLWRlc2NyaXB0aW9uIHsKICBmb250LXNpemU6IDEzcHg7CiAgY29sb3I6ICM5OTk7CiAgbWFyZ2luOiAxNnB4IDA7CiAgcGFkZGluZzogMTJweDsKICBiYWNrZ3JvdW5kOiAjZjhmOWZhOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBsaW5lLWhlaWdodDogMS41Owp9CgoucmVtaW5kZXItcG9wdXAtZm9vdGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7CiAgZ2FwOiAxMnB4OwogIHBhZGRpbmc6IDE2cHggMjBweDsKICBib3JkZXItdG9wOiAxcHggc29saWQgI2YwZjBmMDsKICBiYWNrZ3JvdW5kOiAjZmFmYWZhOwp9CgoucmVtaW5kZXItY2FuY2VsLWJ0biB7CiAgaGVpZ2h0OiAzNnB4OwogIHBhZGRpbmc6IDAgMTZweDsKICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICBib3JkZXI6IDFweCBzb2xpZCAjZDlkOWQ5OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBjb2xvcjogIzY2NjsKICBmb250LXNpemU6IDE0cHg7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRyYW5zaXRpb246IGFsbCAwLjNzOwoKICAmOmhvdmVyIHsKICAgIGNvbG9yOiAjMTg5MGZmOwogICAgYm9yZGVyLWNvbG9yOiAjMTg5MGZmOwogIH0KfQoKLnJlbWluZGVyLWNvbmZpcm0tYnRuIHsKICBoZWlnaHQ6IDM2cHg7CiAgcGFkZGluZzogMCAxNnB4OwogIGJhY2tncm91bmQ6ICMxODkwZmY7CiAgYm9yZGVyOiBub25lOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBjb2xvcjogd2hpdGU7CiAgZm9udC1zaXplOiAxNHB4OwogIGN1cnNvcjogcG9pbnRlcjsKICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kIDAuM3M7CgogICY6aG92ZXIgewogICAgYmFja2dyb3VuZDogIzQwYTlmZjsKICB9CgogICY6YWN0aXZlIHsKICAgIGJhY2tncm91bmQ6ICMwOTZkZDk7CiAgfQp9CgovLyDkuIrkvKBDU1blvLnnqpfmoLflvI8KLnVwbG9hZC1wb3B1cC1vdmVybGF5IHsKICBwb3NpdGlvbjogZml4ZWQ7CiAgdG9wOiAwOwogIGxlZnQ6IDA7CiAgcmlnaHQ6IDA7CiAgYm90dG9tOiAwOwogIGJhY2tncm91bmQ6IHJnYmEoMCwgMCwgMCwgMC41KTsKICB6LWluZGV4OiA5OTk5OwogIGRpc3BsYXk6IGZsZXg7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKfQoKLnVwbG9hZC1wb3B1cCB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIHdpZHRoOiA0ODBweDsKICBtYXgtd2lkdGg6IDkwdnc7CiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMTUpOwogIG92ZXJmbG93OiBoaWRkZW47Cn0KCi51cGxvYWQtcG9wdXAtaGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDE2cHggMjBweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2YwZjBmMDsKICBiYWNrZ3JvdW5kOiAjZmFmYWZhOwp9CgoudXBsb2FkLXBvcHVwLXRpdGxlIHsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICBjb2xvcjogIzMzMzsKfQoKLnVwbG9hZC1wb3B1cC1jbG9zZSB7CiAgYmFja2dyb3VuZDogbm9uZTsKICBib3JkZXI6IG5vbmU7CiAgZm9udC1zaXplOiAyMHB4OwogIGNvbG9yOiAjOTk5OwogIGN1cnNvcjogcG9pbnRlcjsKICBwYWRkaW5nOiAwOwogIHdpZHRoOiAyNHB4OwogIGhlaWdodDogMjRweDsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CgogICY6aG92ZXIgewogICAgY29sb3I6ICM2NjY7CiAgfQp9CgoudXBsb2FkLXBvcHVwLWNvbnRlbnQgewogIHBhZGRpbmc6IDIwcHg7Cn0KCi51cGxvYWQtZm9ybS1pdGVtIHsKICBtYXJnaW4tYm90dG9tOiAxNnB4OwoKICAudXBsb2FkLWxhYmVsIHsKICAgIGRpc3BsYXk6IGJsb2NrOwogICAgZm9udC1zaXplOiAxNHB4OwogICAgY29sb3I6ICMzMzM7CiAgICBtYXJnaW4tYm90dG9tOiA4cHg7CiAgICBmb250LXdlaWdodDogNTAwOwogIH0KCiAgLnVwbG9hZC1pbnB1dCB7CiAgICB3aWR0aDogMTAwJTsKICAgIGhlaWdodDogMzZweDsKICAgIHBhZGRpbmc6IDAgMTJweDsKICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7CiAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBjb2xvcjogIzMzMzsKICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgb3V0bGluZTogbm9uZTsKCiAgICAmOmZvY3VzIHsKICAgICAgYm9yZGVyLWNvbG9yOiAjMTg5MGZmOwogICAgICBib3gtc2hhZG93OiAwIDAgMCAycHggcmdiYSgyNCwgMTQ0LCAyNTUsIDAuMik7CiAgICB9CgogICAgJjo6cGxhY2Vob2xkZXIgewogICAgICBjb2xvcjogI2JmYmZiZjsKICAgIH0KICB9CgogIC51cGxvYWQtdGV4dGFyZWEgewogICAgd2lkdGg6IDEwMCU7CiAgICBwYWRkaW5nOiA4cHggMTJweDsKICAgIGJvcmRlcjogMXB4IHNvbGlkICNkOWQ5ZDk7CiAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICBmb250LXNpemU6IDE0cHg7CiAgICBjb2xvcjogIzMzMzsKICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgb3V0bGluZTogbm9uZTsKICAgIHJlc2l6ZTogdmVydGljYWw7CiAgICBtaW4taGVpZ2h0OiA4MHB4OwoKICAgICY6Zm9jdXMgewogICAgICBib3JkZXItY29sb3I6ICMxODkwZmY7CiAgICAgIGJveC1zaGFkb3c6IDAgMCAwIDJweCByZ2JhKDI0LCAxNDQsIDI1NSwgMC4yKTsKICAgIH0KCiAgICAmOjpwbGFjZWhvbGRlciB7CiAgICAgIGNvbG9yOiAjYmZiZmJmOwogICAgfQogIH0KCiAgLnVwbG9hZC1maWxlLXNlY3Rpb24gewogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBnYXA6IDEycHg7CgogICAgLnVwbG9hZC1maWxlLWlucHV0IHsKICAgICAgZGlzcGxheTogbm9uZTsKICAgIH0KCiAgICAudXBsb2FkLWZpbGUtYnV0dG9uIHsKICAgICAgaGVpZ2h0OiAzNnB4OwogICAgICBwYWRkaW5nOiAwIDE2cHg7CiAgICAgIGJhY2tncm91bmQ6ICMxODkwZmY7CiAgICAgIGJvcmRlcjogbm9uZTsKICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICBjb2xvcjogd2hpdGU7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kIDAuM3M7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwoKICAgICAgJjpob3ZlciB7CiAgICAgICAgYmFja2dyb3VuZDogIzQwYTlmZjsKICAgICAgfQoKICAgICAgJjphY3RpdmUgewogICAgICAgIGJhY2tncm91bmQ6ICMwOTZkZDk7CiAgICAgIH0KICAgIH0KCiAgICAudXBsb2FkLWZpbGUtbmFtZSB7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgICAgY29sb3I6ICMzMzM7CiAgICAgIGZsZXg6IDE7CiAgICB9CgogICAgLnVwbG9hZC1maWxlLXBsYWNlaG9sZGVyIHsKICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICBjb2xvcjogI2JmYmZiZjsKICAgICAgZmxleDogMTsKICAgIH0KICB9Cn0KCi51cGxvYWQtdGlwcyB7CiAgbWFyZ2luLXRvcDogMjBweDsKICBwYWRkaW5nOiAxMnB4OwogIGJhY2tncm91bmQ6ICNmOGY5ZmE7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGJvcmRlci1sZWZ0OiAzcHggc29saWQgIzE4OTBmZjsKCiAgLnVwbG9hZC10aXBzLXRpdGxlIHsKICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIGZvbnQtd2VpZ2h0OiA1MDA7CiAgICBjb2xvcjogIzMzMzsKICAgIG1hcmdpbi1ib3R0b206IDhweDsKICB9CgogIC51cGxvYWQtdGlwcy1jb250ZW50IHsKICAgIGZvbnQtc2l6ZTogMTNweDsKICAgIGNvbG9yOiAjNjY2OwogICAgbGluZS1oZWlnaHQ6IDEuNjsKICB9Cn0KCi51cGxvYWQtcG9wdXAtZm9vdGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogZmxleC1lbmQ7CiAgZ2FwOiAxMnB4OwogIHBhZGRpbmc6IDE2cHggMjBweDsKICBib3JkZXItdG9wOiAxcHggc29saWQgI2YwZjBmMDsKICBiYWNrZ3JvdW5kOiAjZmFmYWZhOwp9CgoudXBsb2FkLWNhbmNlbC1idG4gewogIGhlaWdodDogMzZweDsKICBwYWRkaW5nOiAwIDE2cHg7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgYm9yZGVyOiAxcHggc29saWQgI2Q5ZDlkOTsKICBib3JkZXItcmFkaXVzOiA0cHg7CiAgY29sb3I6ICM2NjY7CiAgZm9udC1zaXplOiAxNHB4OwogIGN1cnNvcjogcG9pbnRlcjsKICB0cmFuc2l0aW9uOiBhbGwgMC4zczsKCiAgJjpob3ZlciB7CiAgICBjb2xvcjogIzE4OTBmZjsKICAgIGJvcmRlci1jb2xvcjogIzE4OTBmZjsKICB9Cn0KCi51cGxvYWQtY29uZmlybS1idG4gewogIGhlaWdodDogMzZweDsKICBwYWRkaW5nOiAwIDE2cHg7CiAgYmFja2dyb3VuZDogIzE4OTBmZjsKICBib3JkZXI6IG5vbmU7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGNvbG9yOiB3aGl0ZTsKICBmb250LXNpemU6IDE0cHg7CiAgY3Vyc29yOiBwb2ludGVyOwogIHRyYW5zaXRpb246IGJhY2tncm91bmQgMC4zczsKCiAgJjpob3ZlciB7CiAgICBiYWNrZ3JvdW5kOiAjNDBhOWZmOwogIH0KCiAgJjphY3RpdmUgewogICAgYmFja2dyb3VuZDogIzA5NmRkOTsKICB9Cn0KCi8vIOiuvue9ruW8ueeql+agt+W8jwouc2V0dGluZ3MtcG9wdXAgewogIGJhY2tncm91bmQ6ICNmZmZmZmY7CiAgYm9yZGVyOiAxcHggc29saWQgI2U4ZThlODsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpOwogIHBhZGRpbmc6IDhweDsKICBvdmVyZmxvdzogaGlkZGVuOwoKICAuc2V0dGluZ3MtcG9wdXAtY29udGVudCB7CiAgICAuY2hhcnQtdHlwZXMtZ3JpZCB7CiAgICAgIGRpc3BsYXk6IGdyaWQ7CiAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDQsIDFmcik7CiAgICAgIGdhcDogOHB4OwoKICAgICAgLmNoYXJ0LXR5cGUtaXRlbSB7CiAgICAgICAgZGlzcGxheTogZmxleDsKICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgICAgcGFkZGluZzogOHB4IDRweDsKICAgICAgICBjdXJzb3I6IHBvaW50ZXI7CiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnM7CiAgICAgICAgYm9yZGVyLXJhZGl1czogNHB4OwogICAgICAgIG1pbi1oZWlnaHQ6IDYwcHg7CgogICAgICAgICY6aG92ZXIgewogICAgICAgICAgYmFja2dyb3VuZDogI2YwZjhmZjsKICAgICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsKICAgICAgICB9CgogICAgICAgICY6YWN0aXZlIHsKICAgICAgICAgIGJhY2tncm91bmQ6ICNlNmY3ZmY7CiAgICAgICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMCk7CiAgICAgICAgfQoKICAgICAgICAuY2hhcnQtaWNvbiB7CiAgICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgICAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgICAgICAgbWFyZ2luLWJvdHRvbTogNHB4OwoKICAgICAgICAgIHN2ZyB7CiAgICAgICAgICAgIHdpZHRoOiAyMHB4OwogICAgICAgICAgICBoZWlnaHQ6IDIwcHg7CiAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzOwogICAgICAgICAgfQogICAgICAgIH0KCiAgICAgICAgLmNoYXJ0LWxhYmVsIHsKICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsKICAgICAgICAgIGNvbG9yOiAjMjYyNjI2OwogICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyOwogICAgICAgICAgbGluZS1oZWlnaHQ6IDEuMjsKICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7CiAgICAgICAgfQoKICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgIC5jaGFydC1pY29uIHN2ZyB7CiAgICAgICAgICAgIHRyYW5zZm9ybTogc2NhbGUoMS4xKTsKICAgICAgICAgIH0KCiAgICAgICAgICAuY2hhcnQtbGFiZWwgewogICAgICAgICAgICBjb2xvcjogIzE4OTBmZjsKICAgICAgICAgIH0KICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyyCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/datasearch", "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <!-- 顶部搜索栏 -->\n    <div class=\"search-container\">\n      <div class=\"search-form\">\n        <div class=\"search-input-wrapper\">\n          <input\n            type=\"text\"\n            v-model=\"searchForm.keyword\"\n            class=\"search-input\"\n            placeholder=\"搜索 门店营业额 前十 门店 营业额\"\n          />\n        </div>\n        <div class=\"search-buttons\">\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleClose\">\n            <i class=\"close-icon\"></i>\n          </button>\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleSearch\">\n            <i class=\"search-icon\"></i>\n          </button>\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleMinus\">\n            <i class=\"minus-icon\"></i>\n          </button>\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleFilter\" ref=\"filterButton\">\n            <i class=\"filter-icon\"></i>\n          </button>\n        </div>\n\n        <!-- 筛选弹窗 -->\n        <div v-if=\"showFilterPopup\" class=\"filter-popup\" :style=\"filterPopupStyle\">\n          <div class=\"popup-header\">\n            <span>数据</span>\n            <button class=\"popup-close\" @click=\"closeFilterPopup\">×</button>\n          </div>\n          <div class=\"popup-search\">\n            <input type=\"text\" class=\"search-input\" placeholder=\"搜索\" v-model=\"filterSearchQuery\">\n            <i class=\"search-icon\"></i>\n          </div>\n          <div class=\"popup-tabs\">\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '维度' }\" @click=\"activeTab = '维度'\">维度</div>\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '时间维度' }\" @click=\"activeTab = '时间维度'\">时间维度</div>\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '指标' }\" @click=\"activeTab = '指标'\">指标</div>\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '分析' }\" @click=\"activeTab = '分析'\">分析</div>\n          </div>\n          <div class=\"popup-content\">\n            <div v-if=\"activeTab === '维度'\" class=\"tab-content\">\n              <div class=\"filter-item\" @click=\"selectFilter('门店')\">\n                <span>门店</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('品牌')\">\n                <span>品牌</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('等')\">\n                <span>等</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('综合分析')\">\n                <span>综合分析</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('门店营业额')\">\n                <span>门店营业额</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('数据分析')\">\n                <span>数据分析</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n            </div>\n            <div v-if=\"activeTab === '时间维度'\" class=\"tab-content\">\n              <div class=\"filter-item\">\n                <span>日期</span>\n                <i class=\"arrow-icon down\">v</i>\n              </div>\n              <div class=\"time-units-row\">\n                <span class=\"time-unit\" @click=\"selectFilter('日')\">日</span>\n                <span class=\"time-unit\" @click=\"selectFilter('周')\">周</span>\n                <span class=\"time-unit\" @click=\"selectFilter('月')\">月</span>\n                <span class=\"time-unit\" @click=\"selectFilter('季')\">季</span>\n                <span class=\"time-unit\" @click=\"selectFilter('年')\">年</span>\n              </div>\n              <div class=\"time-item\">当日</div>\n              <div class=\"time-item\">数天</div>\n              <div class=\"time-item\">数十天</div>\n              <div class=\"time-item\">数月</div>\n              <div class=\"time-item\">2月1日至16日</div>\n              <div class=\"time-item\">2月1日至今</div>\n            </div>\n            <div v-if=\"activeTab === '指标'\" class=\"tab-content\">\n              <div class=\"filter-item\" @click=\"selectFilter('出店')\">出店</div>\n              <div class=\"filter-item\" @click=\"selectFilter('进店顾客')\">进店顾客</div>\n              <div class=\"filter-item\" @click=\"selectFilter('客单')\">客单</div>\n              <div class=\"filter-item\" @click=\"selectFilter('分析')\">分析</div>\n              <div class=\"filter-item\" @click=\"selectFilter('利润')\">利润</div>\n              <div class=\"filter-item\" @click=\"selectFilter('销售额')\">销售额</div>\n              <div class=\"filter-item\" @click=\"selectFilter('进货数量')\">进货数量</div>\n              <div class=\"filter-item\" @click=\"selectFilter('退货数量')\">退货数量</div>\n              <div class=\"filter-item\" @click=\"selectFilter('总价值')\">总价值</div>\n              <div class=\"filter-item\" @click=\"selectFilter('公司利润率')\">公司利润率</div>\n              <div class=\"filter-item\" @click=\"selectFilter('客户数量')\">客户数量</div>\n              <div class=\"filter-item\" @click=\"selectFilter('今日利润')\">今日利润</div>\n              <div class=\"filter-item\" @click=\"selectFilter('全店成本率')\">全店成本率</div>\n            </div>\n            <div v-if=\"activeTab === '分析'\" class=\"tab-content\">\n              <div class=\"filter-item\" @click=\"selectFilter('出店')\">出店</div>\n              <div class=\"filter-item\" @click=\"selectFilter('增长')\">增长</div>\n              <div class=\"filter-item\" @click=\"selectFilter('开店')\">开店</div>\n              <div class=\"filter-item\" @click=\"selectFilter('同比')\">同比</div>\n              <div class=\"filter-item\" @click=\"selectFilter('成交率')\">成交率</div>\n              <div class=\"filter-item\" @click=\"selectFilter('分析')\">分析</div>\n              <div class=\"filter-item\" @click=\"selectFilter('同比')\">同比</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 顶部：门店营业额前十的 + 智能助手 -->\n    <div class=\"top-section\">\n      <div class=\"chart-card main-chart\">\n        <div class=\"chart-header\">\n          <div class=\"chart-title\">\n            <i class=\"chart-icon\"></i>\n            <span>门店营业额前十的</span>\n            <i class=\"help-icon\">?</i>\n          </div>\n          <div class=\"chart-meta\">\n            <span class=\"chart-date\">2024-01-01 至 12-31</span>\n            <span class=\"chart-type\">月报</span>\n            <span class=\"chart-source\">按营业额排序</span>\n          </div>\n          <div class=\"chart-actions\">\n            <i class=\"action-icon refresh\" @click=\"handleRefresh\"></i>\n            <i class=\"action-icon download\" @click=\"handleDownload\"></i>\n            <i class=\"action-icon more\" ref=\"moreButton1\" @click=\"handleMoreClick($event)\"></i>\n            <i class=\"action-icon settings\" @click=\"handleSettings\"></i>\n            <span class=\"chart-status\">数据加载中</span>\n            <i class=\"action-icon close\" @click=\"handleClose\"></i>\n          </div>\n        </div>\n        <div class=\"chart-content\">\n          <div class=\"chart-legend\">\n            <div class=\"legend-item\">\n              <span class=\"legend-color blue\"></span>\n              <span>营业额/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color yellow\"></span>\n              <span>利润/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color line\"></span>\n              <span>营业额同比增长率</span>\n            </div>\n          </div>\n          <div class=\"chart-wrapper\">\n            <div ref=\"storeRevenueChart\" class=\"chart\" style=\"height: 300px;\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能助手面板 -->\n      <div class=\"assistant-panel\">\n        <div class=\"panel-header\">\n          <span>智能助手</span>\n          <i class=\"panel-close\">×</i>\n        </div>\n        <div class=\"panel-content\">\n          <div class=\"assistant-item\">\n            <div class=\"assistant-icon\">💬</div>\n            <div class=\"assistant-text\">请问您想了解什么？</div>\n          </div>\n          <div class=\"assistant-item\">\n            <div class=\"assistant-icon\">📊</div>\n            <div class=\"assistant-text\">深圳门店营业额最高，有什么成功经验可以分享？</div>\n          </div>\n          <div class=\"assistant-item\">\n            <div class=\"assistant-icon\">📈</div>\n            <div class=\"assistant-text\">如何提升其他门店的营业额？</div>\n          </div>\n          <div class=\"assistant-item\">\n            <div class=\"assistant-icon\">🎯</div>\n            <div class=\"assistant-text\">引用数据分析</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 中间：营业额同比 单独一行 -->\n    <div class=\"middle-section\">\n      <div class=\"value-card\">\n        <div class=\"value-header\">\n          <div class=\"value-title\">\n            <i class=\"chart-icon\"></i>\n            <span>营业额同比</span>\n            <i class=\"help-icon\">?</i>\n          </div>\n          <div class=\"value-meta\">\n            <span class=\"value-date\">2024-01-01 至 12-31</span>\n            <span class=\"value-type\">月报</span>\n          </div>\n          <div class=\"value-actions\">\n            <i class=\"action-icon refresh\" @click=\"handleRefresh\"></i>\n            <i class=\"action-icon more\" ref=\"moreButton2\" @click=\"handleMoreClick($event)\"></i>\n            <i class=\"action-icon settings\" @click=\"handleSettings\"></i>\n          </div>\n        </div>\n        <div class=\"value-content\">\n          <div class=\"value-main\">\n            <span class=\"value-label\">营业额(总) / 元</span>\n            <div class=\"value-number\">165.32<span class=\"value-unit\">亿</span></div>\n            <div class=\"value-change\">\n              <span class=\"change-text\">同比上期</span>\n              <span class=\"change-value positive\">+4.73%(+7.43亿)</span>\n              <i class=\"change-arrow up\"></i>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 底部：品牌门店营业额前十的 + 智能助手 -->\n    <div class=\"bottom-section\">\n      <div class=\"chart-card main-chart\">\n        <div class=\"chart-header\">\n          <div class=\"chart-title\">\n            <i class=\"chart-icon\"></i>\n            <span>品牌门店营业额前十的</span>\n            <i class=\"help-icon\">?</i>\n          </div>\n          <div class=\"chart-meta\">\n            <span class=\"chart-date\">2024-01-01 至 12-31</span>\n            <span class=\"chart-type\">月报</span>\n            <span class=\"chart-source\">按营业额排序</span>\n          </div>\n          <div class=\"chart-actions\">\n            <i class=\"action-icon refresh\" @click=\"handleRefresh\"></i>\n            <i class=\"action-icon download\" @click=\"handleDownload\"></i>\n            <i class=\"action-icon more\" ref=\"moreButton3\" @click=\"handleMoreClick($event)\"></i>\n            <i class=\"action-icon settings\" @click=\"handleSettings\"></i>\n            <span class=\"chart-status\">数据加载中</span>\n            <i class=\"action-icon close\" @click=\"handleClose\"></i>\n          </div>\n        </div>\n        <div class=\"chart-content\">\n          <div class=\"chart-legend\">\n            <div class=\"legend-item\">\n              <span class=\"legend-color blue\"></span>\n              <span>营业额/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color yellow\"></span>\n              <span>利润/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color line\"></span>\n              <span>营业额同比增长率</span>\n            </div>\n          </div>\n          <div class=\"chart-wrapper\">\n            <div ref=\"cloudRevenueChart\" class=\"chart\" style=\"height: 300px;\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能助手面板 -->\n      <div class=\"assistant-panel\">\n        <div class=\"panel-header\">\n          <span>智能助手</span>\n          <i class=\"panel-close\">×</i>\n        </div>\n        <div class=\"panel-content\">\n          <div class=\"assistant-item\">\n            <div class=\"assistant-icon\">💬</div>\n            <div class=\"assistant-text\">请问您想了解什么？</div>\n          </div>\n          <div class=\"assistant-item\">\n            <div class=\"assistant-icon\">📊</div>\n            <div class=\"assistant-text\">华南大区表现突出，有什么运营策略？</div>\n          </div>\n          <div class=\"assistant-item\">\n            <div class=\"assistant-icon\">📈</div>\n            <div class=\"assistant-text\">如何改善华东大区的负增长？</div>\n          </div>\n          <div class=\"assistant-item\">\n            <div class=\"assistant-icon\">🎯</div>\n            <div class=\"assistant-text\">引用数据分析</div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 更多操作弹窗 -->\n    <div v-if=\"showMorePopup\" class=\"more-popup\" :style=\"morePopupStyle\" @click.stop>\n      <div class=\"more-popup-content\">\n        <div class=\"more-action-item\" @click.stop=\"handleCardReminder\">卡片提醒</div>\n        <div class=\"more-action-item\" @click.stop=\"handleShareCard\">分享卡片</div>\n        <div class=\"more-action-item\" @click=\"handleSaveCard\">保存卡片</div>\n        <div class=\"more-action-item\" @click.stop=\"handleUploadCSV\">上传CSV</div>\n        <div class=\"more-action-item\" @click=\"handleDownloadPNG\">下载PNG</div>\n      </div>\n    </div>\n\n    <!-- 分享卡片弹窗 -->\n    <div v-if=\"showSharePopup\" class=\"share-popup-overlay\" @click=\"closeSharePopup\">\n      <div class=\"share-popup\" @click.stop>\n        <div class=\"share-popup-header\">\n          <span class=\"share-popup-title\">分享链接</span>\n          <button class=\"share-popup-close\" @click=\"closeSharePopup\">×</button>\n        </div>\n        <div class=\"share-popup-content\">\n          <div class=\"share-description\">\n            分享分析结果，让更多的人看到你的洞察\n          </div>\n          <div class=\"share-option\">\n            <div class=\"share-option-label\">\n              <span>代码嵌入功能</span>\n            </div>\n            <div class=\"share-toggle\">\n              <input type=\"checkbox\" id=\"embedToggle\" v-model=\"embedEnabled\" class=\"toggle-input\">\n              <label for=\"embedToggle\" class=\"toggle-label\"></label>\n            </div>\n          </div>\n          <div class=\"share-link-section\">\n            <input\n              type=\"text\"\n              class=\"share-link-input\"\n              :value=\"shareLink\"\n              readonly\n              placeholder=\"https://dwz.cn/jzwMdMh\"\n            >\n            <button class=\"copy-link-btn\" @click=\"copyShareLink\">复制链接</button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 卡片提醒弹窗 -->\n    <div v-if=\"showReminderPopup\" class=\"reminder-popup-overlay\" @click=\"closeReminderPopup\">\n      <div class=\"reminder-popup\" @click.stop>\n        <div class=\"reminder-popup-header\">\n          <span class=\"reminder-popup-title\">卡片提醒设置</span>\n          <button class=\"reminder-popup-close\" @click=\"closeReminderPopup\">×</button>\n        </div>\n        <div class=\"reminder-popup-content\">\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">提醒卡片</label>\n            <select class=\"reminder-select\" v-model=\"reminderForm.cardName\">\n              <option value=\"\">请选择卡片</option>\n              <option value=\"门店营业额前十\">门店营业额前十</option>\n              <option value=\"云营业额前十\">云营业额前十</option>\n            </select>\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">提醒邮箱地址</label>\n            <input\n              type=\"email\"\n              class=\"reminder-input\"\n              v-model=\"reminderForm.email\"\n              placeholder=\"请输入邮箱地址\"\n            >\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">数据变化</label>\n            <div class=\"reminder-change-section\">\n              <select class=\"reminder-select-small\" v-model=\"reminderForm.changeType\">\n                <option value=\"同比增减幅\">同比增减幅/元</option>\n                <option value=\"环比增减幅\">环比增减幅/元</option>\n              </select>\n              <select class=\"reminder-select-small\" v-model=\"reminderForm.timePeriod\">\n                <option value=\"天数\">天数(天)</option>\n                <option value=\"周数\">周数(周)</option>\n                <option value=\"月数\">月数(月)</option>\n              </select>\n            </div>\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <div class=\"reminder-threshold-section\">\n              <input\n                type=\"number\"\n                class=\"reminder-number-input\"\n                v-model=\"reminderForm.threshold\"\n                placeholder=\"0\"\n              >\n              <span class=\"reminder-unit\">元</span>\n              <div class=\"reminder-checkbox-section\">\n                <input\n                  type=\"checkbox\"\n                  id=\"contentChange\"\n                  v-model=\"reminderForm.contentChange\"\n                  class=\"reminder-checkbox\"\n                >\n                <label for=\"contentChange\" class=\"reminder-checkbox-label\">内容变化提醒</label>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"reminder-description\">\n            当选择指标比上月数据变化超过设定阈值时，发送邮件提醒\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">提醒方式</label>\n            <div class=\"reminder-method-section\">\n              <div class=\"reminder-radio-item\">\n                <input\n                  type=\"radio\"\n                  id=\"emailMethod\"\n                  value=\"email\"\n                  v-model=\"reminderForm.method\"\n                  class=\"reminder-radio\"\n                >\n                <label for=\"emailMethod\" class=\"reminder-radio-label\">邮件提醒</label>\n              </div>\n              <div class=\"reminder-radio-item\">\n                <input\n                  type=\"radio\"\n                  id=\"smsMethod\"\n                  value=\"sms\"\n                  v-model=\"reminderForm.method\"\n                  class=\"reminder-radio\"\n                >\n                <label for=\"smsMethod\" class=\"reminder-radio-label\">短信提醒</label>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"reminder-popup-footer\">\n          <button class=\"reminder-cancel-btn\" @click=\"closeReminderPopup\">取消</button>\n          <button class=\"reminder-confirm-btn\" @click=\"confirmReminder\">确定，设置提醒人</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 上传CSV弹窗 -->\n    <div v-if=\"showUploadPopup\" class=\"upload-popup-overlay\" @click=\"closeUploadPopup\">\n      <div class=\"upload-popup\" @click.stop>\n        <div class=\"upload-popup-header\">\n          <span class=\"upload-popup-title\">加入报告</span>\n          <button class=\"upload-popup-close\" @click=\"closeUploadPopup\">×</button>\n        </div>\n        <div class=\"upload-popup-content\">\n          <div class=\"upload-form-item\">\n            <label class=\"upload-label\">报告名称</label>\n            <input\n              type=\"text\"\n              class=\"upload-input\"\n              v-model=\"uploadForm.reportName\"\n              placeholder=\"请输入报告名称\"\n            >\n          </div>\n\n          <div class=\"upload-form-item\">\n            <label class=\"upload-label\">描述信息</label>\n            <textarea\n              class=\"upload-textarea\"\n              v-model=\"uploadForm.description\"\n              placeholder=\"请输入描述信息\"\n              rows=\"3\"\n            ></textarea>\n          </div>\n\n          <div class=\"upload-form-item\">\n            <label class=\"upload-label\">上传文件</label>\n            <div class=\"upload-file-section\">\n              <input\n                type=\"file\"\n                accept=\".csv\"\n                @change=\"handleFileSelect\"\n                class=\"upload-file-input\"\n                id=\"csvFileInput\"\n              >\n              <label for=\"csvFileInput\" class=\"upload-file-button\">\n                选择文件\n              </label>\n              <span class=\"upload-file-name\" v-if=\"uploadForm.file\">\n                {{ uploadForm.file.name }}\n              </span>\n              <span class=\"upload-file-placeholder\" v-else>\n                请选择CSV文件\n              </span>\n            </div>\n          </div>\n\n          <div class=\"upload-tips\">\n            <div class=\"upload-tips-title\">上传说明：</div>\n            <div class=\"upload-tips-content\">\n              • 支持CSV格式文件<br>\n              • 文件大小不超过10MB<br>\n              • 请确保数据格式正确\n            </div>\n          </div>\n        </div>\n\n        <div class=\"upload-popup-footer\">\n          <button class=\"upload-cancel-btn\" @click=\"closeUploadPopup\">取消</button>\n          <button class=\"upload-confirm-btn\" @click=\"confirmUpload\">确定</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 设置弹窗 -->\n    <div v-if=\"showSettingsPopup\" class=\"settings-popup\" :style=\"settingsPopupStyle\" @click.stop>\n      <div class=\"settings-popup-content\">\n        <div class=\"chart-types-grid\">\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('bar')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <rect x=\"3\" y=\"12\" width=\"4\" height=\"9\" fill=\"#1890ff\"/>\n                <rect x=\"10\" y=\"8\" width=\"4\" height=\"13\" fill=\"#1890ff\"/>\n                <rect x=\"17\" y=\"4\" width=\"4\" height=\"17\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">柱状图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('line')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M3 17L9 11L13 15L21 7\" stroke=\"#1890ff\" stroke-width=\"2\" fill=\"none\"/>\n                <circle cx=\"3\" cy=\"17\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"9\" cy=\"11\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"13\" cy=\"15\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"21\" cy=\"7\" r=\"2\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">折线图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('pie')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M12 2V12L20.5 7.5C19.5 4.5 16 2 12 2Z\" fill=\"#1890ff\"/>\n                <path d=\"M12 12L20.5 16.5C19.5 19.5 16 22 12 22C7 22 3 18 3 12C3 7 7 3 12 3V12Z\" fill=\"#52c41a\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">饼图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('area')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M3 17L9 11L13 15L21 7V21H3V17Z\" fill=\"#1890ff\" opacity=\"0.3\"/>\n                <path d=\"M3 17L9 11L13 15L21 7\" stroke=\"#1890ff\" stroke-width=\"2\" fill=\"none\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">面积图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('scatter')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <circle cx=\"5\" cy=\"18\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"9\" cy=\"12\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"13\" cy=\"16\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"17\" cy=\"8\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"21\" cy=\"14\" r=\"2\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">散点图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('radar')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <polygon points=\"12,2 20,8 20,16 12,22 4,16 4,8\" stroke=\"#1890ff\" stroke-width=\"1\" fill=\"none\"/>\n                <polygon points=\"12,6 16,9 16,15 12,18 8,15 8,9\" stroke=\"#1890ff\" stroke-width=\"1\" fill=\"#1890ff\" opacity=\"0.3\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">雷达图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('gauge')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\" stroke=\"#1890ff\" stroke-width=\"2\" fill=\"none\"/>\n                <path d=\"M12 12L16 8\" stroke=\"#1890ff\" stroke-width=\"2\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"2\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">仪表盘</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('funnel')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M6 4H18L16 8H8L6 4Z\" fill=\"#1890ff\"/>\n                <path d=\"M8 8H16L14 12H10L8 8Z\" fill=\"#52c41a\"/>\n                <path d=\"M10 12H14L13 16H11L10 12Z\" fill=\"#faad14\"/>\n                <path d=\"M11 16H13V20H11V16Z\" fill=\"#f5222d\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">漏斗图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('heatmap')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <rect x=\"3\" y=\"3\" width=\"4\" height=\"4\" fill=\"#1890ff\"/>\n                <rect x=\"8\" y=\"3\" width=\"4\" height=\"4\" fill=\"#52c41a\"/>\n                <rect x=\"13\" y=\"3\" width=\"4\" height=\"4\" fill=\"#faad14\"/>\n                <rect x=\"18\" y=\"3\" width=\"3\" height=\"4\" fill=\"#f5222d\"/>\n                <rect x=\"3\" y=\"8\" width=\"4\" height=\"4\" fill=\"#52c41a\"/>\n                <rect x=\"8\" y=\"8\" width=\"4\" height=\"4\" fill=\"#faad14\"/>\n                <rect x=\"13\" y=\"8\" width=\"4\" height=\"4\" fill=\"#f5222d\"/>\n                <rect x=\"18\" y=\"8\" width=\"3\" height=\"4\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">热力图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('treemap')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <rect x=\"2\" y=\"2\" width=\"10\" height=\"8\" fill=\"#1890ff\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"13\" y=\"2\" width=\"9\" height=\"5\" fill=\"#52c41a\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"13\" y=\"8\" width=\"9\" height=\"3\" fill=\"#faad14\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"2\" y=\"11\" width=\"6\" height=\"11\" fill=\"#f5222d\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"9\" y=\"11\" width=\"13\" height=\"11\" fill=\"#722ed1\" stroke=\"#fff\" stroke-width=\"1\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">矩形树图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('sunburst')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"none\" stroke=\"#1890ff\" stroke-width=\"2\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"6\" fill=\"none\" stroke=\"#52c41a\" stroke-width=\"2\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"#faad14\"/>\n                <path d=\"M12 2L14 6L12 6L10 6L12 2Z\" fill=\"#1890ff\"/>\n                <path d=\"M22 12L18 14L18 12L18 10L22 12Z\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">旭日图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('sankey')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M2 6C2 6 8 6 12 10C16 14 22 14 22 14\" stroke=\"#1890ff\" stroke-width=\"3\" fill=\"none\"/>\n                <path d=\"M2 12C2 12 8 12 12 12C16 12 22 12 22 12\" stroke=\"#52c41a\" stroke-width=\"3\" fill=\"none\"/>\n                <path d=\"M2 18C2 18 8 18 12 14C16 10 22 10 22 10\" stroke=\"#faad14\" stroke-width=\"3\" fill=\"none\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">桑基图</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts';\n\nexport default {\n  name: \"DataSearch\",\n  data() {\n    return {\n      // 搜索表单数据\n      searchForm: {\n        keyword: '门店 营业额 前十 门店 营业额',\n        dataType: 'store',\n        store: 'all',\n        time: '2024'\n      },\n      // 筛选弹窗相关\n      showFilterPopup: false,\n      filterPopupStyle: {},\n      filterSearchQuery: '',\n      activeTab: '维度',\n      // 更多操作弹窗\n      showMorePopup: false,\n      morePopupStyle: {},\n      // 分享弹窗相关\n      showSharePopup: false,\n      embedEnabled: true,\n      shareLink: 'https://dwz.cn/jzwMdMh',\n\n      // 卡片提醒弹窗\n      showReminderPopup: false,\n      reminderForm: {\n        cardName: '',\n        email: '',\n        changeType: '同比增减幅',\n        timePeriod: '天数',\n        threshold: 0,\n        contentChange: false,\n        method: 'email'\n      },\n      // 上传CSV弹窗\n      showUploadPopup: false,\n      uploadForm: {\n        reportName: '',\n        description: '',\n        file: null\n      },\n      // 图表选择弹窗\n      showChartSelector: false,\n      selectedChartType: 'bar',\n      // 设置弹窗\n      showSettingsPopup: false,\n      settingsPopupStyle: {},\n      // 图表实例\n      storeRevenueChart: null,\n      cloudRevenueChart: null,\n      // 刷新间隔\n      refreshInterval: '0',\n      // 门店营业额数据\n      storeRevenueData: {\n        categories: ['深圳', '广州', '北京', '上海', '杭州', '南京', '成都', '重庆', '武汉', '西安', '天津'],\n        revenue: [21340, 16200, 14100, 8100, 7610, 6200, 5310, 4860, 4340, 3460, 3140],\n        profit: [22410, 18940, 14200, 12400, 7600, 6420, 5400, 4740, 4360, 3740, 3140],\n        growthRate: [11.39, 9.04, 8.31, 7.60, 5.37, 5.04, 4.74, 4.34, 4.17, 3.86, 3.70]\n      },\n      // 云营业额数据\n      cloudRevenueData: {\n        categories: ['华南大区_深圳', '华南大区_广州', '华东大区_上海', '华东大区_杭州', '华北大区_北京', '华中大区_武汉', '西南大区_成都', '西南大区_重庆', '西北大区_西安', '华北大区_天津', '华东大区_南京'],\n        revenue: [6000000, 5800000, 4100000, 4100000, 3400000, 2600000, 2400000, 2100000, 2000000, 1900000, 1800000],\n        profit: [5800000, 5600000, 4100000, 4100000, 3200000, 2400000, 2200000, 2000000, 1900000, 1800000, 1700000],\n        growthRate: [4.70, -0.2, -6.3, -6.3, 1.9, 2.6, 2.7, 2.1, 2.0, 1.9, 1.8]\n      }\n    };\n  },\n  mounted() {\n    this.initCharts();\n    // 添加点击外部关闭弹窗的事件监听\n    document.addEventListener('click', this.handleClickOutside);\n  },\n  beforeDestroy() {\n    if (this.storeRevenueChart) {\n      this.storeRevenueChart.dispose();\n    }\n    if (this.cloudRevenueChart) {\n      this.cloudRevenueChart.dispose();\n    }\n    // 移除事件监听\n    document.removeEventListener('click', this.handleClickOutside);\n  },\n  methods: {\n    /** 初始化图表 */\n    initCharts() {\n      this.$nextTick(() => {\n        this.initStoreRevenueChart();\n        this.initCloudRevenueChart();\n      });\n    },\n\n    /** 初始化门店营业额图表 */\n    initStoreRevenueChart() {\n      this.storeRevenueChart = echarts.init(this.$refs.storeRevenueChart);\n\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          }\n        },\n        legend: {\n          show: false\n        },\n        xAxis: [\n          {\n            type: 'category',\n            data: this.storeRevenueData.categories,\n            axisPointer: {\n              type: 'shadow'\n            },\n            axisLabel: {\n              rotate: 45,\n              fontSize: 10\n            }\n          }\n        ],\n        yAxis: [\n          {\n            type: 'value',\n            name: '营业额(万元)',\n            position: 'left',\n            axisLabel: {\n              formatter: function(value) {\n                if (value >= 10000) {\n                  return (value / 10000).toFixed(1) + '万';\n                }\n                return value;\n              }\n            }\n          },\n          {\n            type: 'value',\n            name: '营业额同比增长率',\n            position: 'right',\n            axisLabel: {\n              formatter: '{value}%'\n            }\n          }\n        ],\n        series: [\n          {\n            name: '营业额/万元',\n            type: 'bar',\n            data: this.storeRevenueData.revenue,\n            itemStyle: {\n              color: '#5B8FF9'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(1) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '利润/万元',\n            type: 'bar',\n            data: this.storeRevenueData.profit,\n            itemStyle: {\n              color: '#FFD666'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(1) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '营业额同比增长率',\n            type: 'line',\n            yAxisIndex: 1,\n            data: this.storeRevenueData.growthRate,\n            itemStyle: {\n              color: '#FF6B6B'\n            },\n            lineStyle: {\n              width: 2\n            },\n            label: {\n              show: true,\n              position: 'top',\n              formatter: '{c}%',\n              fontSize: 10\n            }\n          }\n        ]\n      };\n\n      this.storeRevenueChart.setOption(option);\n    },\n\n    /** 初始化云营业额图表 */\n    initCloudRevenueChart() {\n      this.cloudRevenueChart = echarts.init(this.$refs.cloudRevenueChart);\n\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          }\n        },\n        legend: {\n          show: false\n        },\n        xAxis: [\n          {\n            type: 'category',\n            data: this.cloudRevenueData.categories,\n            axisPointer: {\n              type: 'shadow'\n            },\n            axisLabel: {\n              rotate: 45,\n              fontSize: 10\n            }\n          }\n        ],\n        yAxis: [\n          {\n            type: 'value',\n            name: '营业额(万元)',\n            position: 'left',\n            axisLabel: {\n              formatter: function(value) {\n                return (value / 10000).toFixed(0) + '万';\n              }\n            }\n          },\n          {\n            type: 'value',\n            name: '营业额同比增长率',\n            position: 'right',\n            axisLabel: {\n              formatter: '{value}%'\n            }\n          }\n        ],\n        series: [\n          {\n            name: '营业额/万元',\n            type: 'bar',\n            data: this.cloudRevenueData.revenue,\n            itemStyle: {\n              color: '#5B8FF9'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(0) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '利润/万元',\n            type: 'bar',\n            data: this.cloudRevenueData.profit,\n            itemStyle: {\n              color: '#FFD666'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(0) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '营业额同比增长率',\n            type: 'line',\n            yAxisIndex: 1,\n            data: this.cloudRevenueData.growthRate,\n            itemStyle: {\n              color: '#FF6B6B'\n            },\n            lineStyle: {\n              width: 2\n            },\n            label: {\n              show: true,\n              position: 'top',\n              formatter: '{c}%',\n              fontSize: 10\n            }\n          }\n        ]\n      };\n\n      this.cloudRevenueChart.setOption(option);\n    },\n\n    // 搜索栏图标按钮方法\n    handleClose() {\n      console.log('关闭搜索');\n      this.$message.info('关闭搜索');\n    },\n\n    handleSearch() {\n      console.log('执行搜索');\n      this.$message.success('搜索功能开发中...');\n    },\n\n    handleMinus() {\n      console.log('减号操作');\n      this.$message.info('减号功能开发中...');\n    },\n\n    handleFilter() {\n      console.log('筛选功能');\n      this.showFilterPopup = !this.showFilterPopup;\n\n      if (this.showFilterPopup) {\n        this.$nextTick(() => {\n          this.setFilterPopupPosition();\n        });\n      }\n    },\n\n    // 设置弹窗位置\n    setFilterPopupPosition() {\n      const filterButton = this.$refs.filterButton;\n      if (filterButton) {\n        const rect = filterButton.getBoundingClientRect();\n        this.filterPopupStyle = {\n          position: 'fixed',\n          top: rect.bottom + 5 + 'px',\n          right: (window.innerWidth - rect.right) + 'px',\n          zIndex: 1000\n        };\n      }\n    },\n\n    // 关闭筛选弹窗\n    closeFilterPopup() {\n      this.showFilterPopup = false;\n    },\n\n    // 选择筛选项\n    selectFilter(item) {\n      console.log('选择筛选项:', item);\n      this.$message.success(`已选择: ${item}`);\n      this.closeFilterPopup();\n    },\n\n    // 点击外部关闭弹窗\n    handleClickOutside(event) {\n      // 处理筛选弹窗\n      if (this.showFilterPopup) {\n        const filterButton = this.$refs.filterButton;\n        const popup = event.target.closest('.filter-popup');\n\n        // 如果点击的不是筛选按钮也不是弹窗内部，则关闭弹窗\n        if (!filterButton?.contains(event.target) && !popup) {\n          this.closeFilterPopup();\n        }\n      }\n\n      // 处理更多操作弹窗\n      if (this.showMorePopup) {\n        const moreButtons = [\n          this.$refs.moreButton1,\n          this.$refs.moreButton2,\n          this.$refs.moreButton3\n        ];\n        const morePopup = event.target.closest('.more-popup');\n\n        // 检查是否点击了任何更多按钮\n        const clickedMoreButton = moreButtons.some(button =>\n          button && button.contains(event.target)\n        );\n\n        // 如果点击的不是更多按钮也不是弹窗内部，则关闭弹窗\n        if (!clickedMoreButton && !morePopup) {\n          this.closeMorePopup();\n        }\n      }\n\n      // 处理分享弹窗\n      if (this.showSharePopup) {\n        const sharePopup = event.target.closest('.share-popup');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!sharePopup) {\n          this.closeSharePopup();\n        }\n      }\n\n      // 处理卡片提醒弹窗\n      if (this.showReminderPopup) {\n        const reminderPopup = event.target.closest('.reminder-popup');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!reminderPopup) {\n          this.closeReminderPopup();\n        }\n      }\n\n      // 处理上传CSV弹窗\n      if (this.showUploadPopup) {\n        const uploadPopup = event.target.closest('.upload-popup');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!uploadPopup) {\n          this.closeUploadPopup();\n        }\n      }\n\n      // 处理图表选择器弹窗\n      if (this.showChartSelector) {\n        const chartSelector = event.target.closest('.chart-selector');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!chartSelector) {\n          this.closeChartSelector();\n        }\n      }\n\n      // 处理设置弹窗\n      if (this.showSettingsPopup) {\n        const settingsPopup = event.target.closest('.settings-popup');\n        const settingsButtons = document.querySelectorAll('.action-icon.settings');\n\n        // 检查是否点击了任何设置按钮\n        const clickedSettingsButton = Array.from(settingsButtons).some(button =>\n          button && button.contains(event.target)\n        );\n\n        // 如果点击的不是设置按钮也不是弹窗内部，则关闭弹窗\n        if (!clickedSettingsButton && !settingsPopup) {\n          this.closeSettingsPopup();\n        }\n      }\n    },\n\n    // 更多操作相关方法\n    handleMoreClick(event) {\n      this.showMorePopup = true;\n\n      this.$nextTick(() => {\n        this.setMorePopupPosition(event.target);\n      });\n    },\n\n    // 设置更多弹窗位置\n    setMorePopupPosition(buttonElement) {\n      if (buttonElement) {\n        const rect = buttonElement.getBoundingClientRect();\n        this.morePopupStyle = {\n          position: 'fixed',\n          top: rect.bottom + 5 + 'px',\n          left: rect.left - 60 + 'px', // 向左偏移一些，让弹窗居中对齐按钮\n          zIndex: 2000\n        };\n      }\n    },\n\n    closeMorePopup() {\n      this.showMorePopup = false;\n    },\n\n    handleCardReminder() {\n      this.showReminderPopup = true;\n      this.closeMorePopup();\n    },\n\n    handleShareCard() {\n      this.showSharePopup = true;\n      this.closeMorePopup();\n      this.$message.success('分享弹窗已打开');\n    },\n\n    handleSaveCard() {\n      console.log('保存卡片');\n      this.$message.success('保存卡片功能开发中...');\n      this.closeMorePopup();\n    },\n\n    handleUploadCSV() {\n      this.showUploadPopup = true;\n      this.closeMorePopup();\n    },\n\n    handleDownloadPNG() {\n      console.log('下载PNG');\n      this.$message.success('下载PNG功能开发中...');\n      this.closeMorePopup();\n    },\n\n    // 图表按钮处理方法\n    handleRefresh() {\n      console.log('刷新数据');\n      this.$message.success('正在刷新数据...');\n      // 重新初始化图表\n      this.initCharts();\n    },\n\n    handleDownload() {\n      console.log('下载数据');\n      this.$message.success('下载功能开发中...');\n    },\n\n    handleSettings(event) {\n      console.log('设置');\n      this.showSettingsPopup = true;\n\n      this.$nextTick(() => {\n        this.setSettingsPopupPosition(event.target);\n      });\n    },\n\n    // 分享弹窗相关方法\n    closeSharePopup() {\n      this.showSharePopup = false;\n    },\n\n    copyShareLink() {\n      // 复制链接到剪贴板\n      navigator.clipboard.writeText(this.shareLink).then(() => {\n        this.$message.success('链接已复制到剪贴板');\n      }).catch(() => {\n        // 降级方案\n        const textArea = document.createElement('textarea');\n        textArea.value = this.shareLink;\n        document.body.appendChild(textArea);\n        textArea.select();\n        document.execCommand('copy');\n        document.body.removeChild(textArea);\n        this.$message.success('链接已复制到剪贴板');\n      });\n    },\n\n    // 卡片提醒弹窗方法\n    closeReminderPopup() {\n      this.showReminderPopup = false;\n      // 重置表单\n      this.reminderForm = {\n        cardName: '',\n        email: '',\n        changeType: '同比增减幅',\n        timePeriod: '天数',\n        threshold: 0,\n        contentChange: false,\n        method: 'email'\n      };\n    },\n\n    confirmReminder() {\n      // 验证表单\n      if (!this.reminderForm.cardName) {\n        this.$message.warning('请选择提醒卡片');\n        return;\n      }\n      if (!this.reminderForm.email) {\n        this.$message.warning('请输入邮箱地址');\n        return;\n      }\n\n      // 这里可以添加邮箱格式验证\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(this.reminderForm.email)) {\n        this.$message.warning('请输入正确的邮箱格式');\n        return;\n      }\n\n      console.log('设置卡片提醒:', this.reminderForm);\n      this.$message.success('卡片提醒设置成功！');\n      this.closeReminderPopup();\n    },\n\n    // 上传CSV弹窗方法\n    closeUploadPopup() {\n      this.showUploadPopup = false;\n      // 重置表单\n      this.uploadForm = {\n        reportName: '',\n        description: '',\n        file: null\n      };\n    },\n\n    handleFileSelect(event) {\n      const file = event.target.files[0];\n      if (file) {\n        // 检查文件类型\n        if (!file.name.toLowerCase().endsWith('.csv')) {\n          this.$message.warning('请选择CSV格式的文件');\n          event.target.value = '';\n          return;\n        }\n        this.uploadForm.file = file;\n      }\n    },\n\n    confirmUpload() {\n      // 验证表单\n      if (!this.uploadForm.reportName.trim()) {\n        this.$message.warning('请输入报告名称');\n        return;\n      }\n      if (!this.uploadForm.file) {\n        this.$message.warning('请选择要上传的CSV文件');\n        return;\n      }\n\n      console.log('上传CSV:', this.uploadForm);\n      this.$message.success('CSV文件上传成功！');\n      this.closeUploadPopup();\n    },\n\n    // 图表选择器相关方法\n    openChartSelector() {\n      this.showChartSelector = true;\n    },\n\n    closeChartSelector() {\n      this.showChartSelector = false;\n    },\n\n    selectChartType(chartType) {\n      this.selectedChartType = chartType;\n      console.log('选择图表类型:', chartType);\n      this.closeChartSelector();\n    },\n\n    // 设置弹窗相关方法\n    setSettingsPopupPosition(buttonElement) {\n      if (buttonElement) {\n        const rect = buttonElement.getBoundingClientRect();\n        this.settingsPopupStyle = {\n          position: 'fixed',\n          top: rect.bottom + 5 + 'px',\n          left: rect.left - 100 + 'px',\n          zIndex: 2000\n        };\n      }\n    },\n\n    closeSettingsPopup() {\n      this.showSettingsPopup = false;\n    },\n\n    selectChartIcon(chartType) {\n      console.log('选择图表类型:', chartType);\n      this.$message.success(`已选择图表类型: ${chartType}`);\n      this.closeSettingsPopup();\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard-container {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: calc(100vh - 84px);\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n// 搜索栏样式\n.search-container {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 4px;\n  padding: 12px 16px;\n  margin-bottom: 16px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n\n  .search-form {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .search-input-wrapper {\n      flex: 1;\n\n      .search-input {\n        width: 100%;\n        height: 32px;\n        padding: 4px 12px;\n        border: 1px solid #d9d9d9;\n        border-radius: 4px;\n        font-size: 14px;\n        color: #333;\n        background: #fff;\n        outline: none;\n        transition: border-color 0.3s;\n\n        &:focus {\n          border-color: #1890ff;\n          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n        }\n\n        &::placeholder {\n          color: #999;\n        }\n      }\n    }\n\n    .search-buttons {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .btn-icon {\n        width: 32px;\n        height: 32px;\n        padding: 6px;\n        border: 1px solid #d9d9d9;\n        border-radius: 4px;\n        background: #fff;\n        cursor: pointer;\n        transition: all 0.3s;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n\n        &:hover {\n          background: #f5f5f5;\n          border-color: #40a9ff;\n        }\n\n        .close-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDRMNCA0TDQgMTJMMTIgMTJMMTIgNFoiIHN0cm9rZT0iIzY2NiIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K') no-repeat center;\n          background-size: contain;\n        }\n\n        .search-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTcgMTJDMy42ODYyOSAxMiAxIDkuMzEzNzEgMSA2QzEgMi42ODYyOSAzLjY4NjI5IDAgNyAwQzEwLjMxMzcgMCAxMyAyLjY4NjI5IDEzIDZDMTMgOS4zMTM3MSAxMC4zMTM3IDEyIDcgMTJaTTcgMTFDOS43NjE0MiAxMSAxMiA4Ljc2MTQyIDEyIDZDMTIgMy4yMzg1OCA5Ljc2MTQyIDEgNyAxQzQuMjM4NTggMSAyIDMuMjM4NTggMiA2QzIgOC43NjE0MiA0LjIzODU4IDExIDcgMTFaIiBmaWxsPSIjNjY2Ii8+CjxwYXRoIGQ9Ik0xMSAxMUwxNSAxNSIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=') no-repeat center;\n          background-size: contain;\n        }\n\n        .minus-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgOEgxMiIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=') no-repeat center;\n          background-size: contain;\n        }\n\n        .filter-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIgM0gxNEwxMCA3VjEzTDYgMTFWN0wyIDNaIiBzdHJva2U9IiM2NjYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==') no-repeat center;\n          background-size: contain;\n        }\n      }\n    }\n  }\n}\n\n// 筛选弹窗样式\n.filter-popup {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  width: 280px;\n  max-height: 450px;\n  overflow: hidden;\n\n  .popup-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 8px 12px;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafafa;\n    border-radius: 6px 6px 0 0;\n\n    span {\n      font-size: 14px;\n      font-weight: 500;\n      color: #262626;\n    }\n\n    .popup-close {\n      background: none;\n      border: none;\n      font-size: 16px;\n      color: #8c8c8c;\n      cursor: pointer;\n      padding: 0;\n      width: 20px;\n      height: 20px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border-radius: 2px;\n\n      &:hover {\n        background: #f5f5f5;\n        color: #595959;\n      }\n    }\n  }\n\n  .popup-search {\n    padding: 12px;\n    border-bottom: 1px solid #f0f0f0;\n    position: relative;\n\n    .search-input {\n      width: 100%;\n      padding: 6px 12px 6px 32px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 13px;\n      outline: none;\n      background: #ffffff;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);\n      }\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n    }\n\n    .search-icon {\n      position: absolute;\n      left: 20px;\n      top: 50%;\n      transform: translateY(-50%);\n      width: 14px;\n      height: 14px;\n      background: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"%23999\" stroke-width=\"2\"><circle cx=\"11\" cy=\"11\" r=\"8\"/><path d=\"m21 21-4.35-4.35\"/></svg>') no-repeat center;\n      background-size: contain;\n    }\n  }\n\n  .popup-tabs {\n    display: flex;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafafa;\n\n    .tab-item {\n      flex: 1;\n      padding: 8px 12px;\n      text-align: center;\n      font-size: 13px;\n      color: #595959;\n      cursor: pointer;\n      transition: all 0.2s;\n      border-bottom: 2px solid transparent;\n\n      &:hover {\n        color: #1890ff;\n        background: #f5f5f5;\n      }\n\n      &.active {\n        color: #1890ff;\n        background: #ffffff;\n        border-bottom-color: #1890ff;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .popup-content {\n    padding: 0;\n    max-height: 300px;\n    overflow-y: auto;\n\n    .tab-content {\n      .filter-item {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 10px 16px;\n        font-size: 14px;\n        color: #262626;\n        cursor: pointer;\n        transition: all 0.2s;\n        border-bottom: 1px solid #f5f5f5;\n        line-height: 1.4;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        &:hover {\n          background: #f0f8ff;\n          color: #1890ff;\n        }\n\n        &:active {\n          background: #e6f7ff;\n          color: #1890ff;\n        }\n\n        .arrow-icon {\n          font-size: 12px;\n          color: #8c8c8c;\n          font-style: normal;\n        }\n      }\n\n      .time-units-row {\n        padding: 10px 16px;\n        border-bottom: 1px solid #f5f5f5;\n        display: flex;\n        gap: 16px;\n\n        .time-unit {\n          font-size: 14px;\n          color: #262626;\n          cursor: pointer;\n          transition: all 0.2s;\n          padding: 4px 8px;\n          border-radius: 4px;\n\n          &:hover {\n            background: #f0f8ff;\n            color: #1890ff;\n          }\n        }\n      }\n\n      .time-item {\n        padding: 10px 16px;\n        font-size: 14px;\n        color: #262626;\n        cursor: pointer;\n        transition: all 0.2s;\n        border-bottom: 1px solid #f5f5f5;\n        line-height: 1.4;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        &:hover {\n          background: #f0f8ff;\n          color: #1890ff;\n        }\n      }\n    }\n  }\n}\n\n.search-form {\n  .form-row {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    flex-wrap: wrap;\n  }\n\n  .form-group {\n    display: flex;\n    align-items: center;\n    gap: 6px;\n\n    .form-label {\n      font-size: 13px;\n      color: #595959;\n      font-weight: 400;\n      white-space: nowrap;\n      margin: 0;\n    }\n\n    .form-input {\n      height: 28px;\n      padding: 0 8px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 13px;\n      color: #262626;\n      background: #ffffff;\n      outline: none;\n      transition: border-color 0.2s;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);\n      }\n\n      &.keyword-input {\n        width: 200px;\n      }\n\n      &.time-input {\n        width: 60px;\n      }\n\n      &::placeholder {\n        color: #bfbfbf;\n        font-size: 13px;\n      }\n    }\n\n    .form-select {\n      height: 28px;\n      padding: 0 20px 0 8px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 13px;\n      color: #262626;\n      background: #ffffff url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\") no-repeat right 6px center/12px 12px;\n      outline: none;\n      appearance: none;\n      cursor: pointer;\n      min-width: 80px;\n      transition: border-color 0.2s;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);\n      }\n    }\n  }\n\n  .form-buttons {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    margin-left: auto;\n\n    .btn {\n      height: 28px;\n      padding: 0 12px;\n      border-radius: 4px;\n      font-size: 13px;\n      font-weight: 400;\n      border: 1px solid;\n      cursor: pointer;\n      outline: none;\n      transition: all 0.2s;\n      white-space: nowrap;\n\n      &.btn-primary {\n        background: #1890ff;\n        border-color: #1890ff;\n        color: #ffffff;\n\n        &:hover {\n          background: #40a9ff;\n          border-color: #40a9ff;\n        }\n      }\n\n      &.btn-default {\n        background: #ffffff;\n        border-color: #d9d9d9;\n        color: #595959;\n\n        &:hover {\n          color: #1890ff;\n          border-color: #1890ff;\n        }\n      }\n\n      &.btn-success {\n        background: #52c41a;\n        border-color: #52c41a;\n        color: #ffffff;\n\n        &:hover {\n          background: #73d13d;\n          border-color: #73d13d;\n        }\n      }\n    }\n  }\n}\n\n// 顶部区域：门店营业额前十的 + 智能助手\n.top-section {\n  display: flex;\n  gap: 20px;\n  align-items: flex-start;\n}\n\n// 中间区域：营业额同比 单独一行\n.middle-section {\n  width: 100%;\n}\n\n// 底部区域：品牌门店营业额前十的 + 智能助手\n.bottom-section {\n  display: flex;\n  gap: 20px;\n  align-items: flex-start;\n}\n\n// 主图表区域\n.main-chart {\n  flex: 1;\n  min-width: 0;\n}\n\n// 智能助手面板\n.assistant-panel {\n  width: 280px;\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  flex-shrink: 0;\n\n  .panel-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 16px 20px;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafbfc;\n\n    span {\n      font-size: 14px;\n      font-weight: 500;\n      color: #262626;\n    }\n\n    .panel-close {\n      cursor: pointer;\n      font-size: 16px;\n      color: #8c8c8c;\n    }\n  }\n\n  .panel-content {\n    padding: 20px;\n\n    .assistant-item {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      padding: 12px 0;\n      border-bottom: 1px solid #f0f0f0;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .assistant-icon {\n        font-size: 20px;\n        width: 32px;\n        height: 32px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: #f5f5f5;\n        border-radius: 6px;\n      }\n\n      .assistant-text {\n        font-size: 14px;\n        color: #262626;\n        line-height: 1.4;\n        flex: 1;\n      }\n    }\n  }\n}\n\n.chart-card, .value-card {\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.chart-header, .value-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.chart-title, .value-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #262626;\n\n  .chart-icon {\n    width: 16px;\n    height: 16px;\n    background: #1890ff;\n    border-radius: 2px;\n  }\n\n  .help-icon {\n    width: 16px;\n    height: 16px;\n    background: #d9d9d9;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 12px;\n    color: #ffffff;\n    cursor: pointer;\n  }\n}\n\n.chart-meta, .value-meta {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 12px;\n  color: #8c8c8c;\n\n  .chart-date, .value-date {\n    color: #595959;\n  }\n\n  .chart-type, .value-type {\n    background: #f0f0f0;\n    padding: 2px 6px;\n    border-radius: 2px;\n  }\n\n  .chart-source {\n    color: #1890ff;\n  }\n}\n\n.chart-actions, .value-actions {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n\n  .action-icon {\n    width: 16px;\n    height: 16px;\n    border-radius: 2px;\n    cursor: pointer;\n    background-size: 12px 12px;\n    background-position: center;\n    background-repeat: no-repeat;\n    transition: all 0.2s;\n\n    &.refresh {\n      background-color: #52c41a;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #73d13d;\n      }\n    }\n\n    &.download {\n      background-color: #1890ff;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #40a9ff;\n      }\n    }\n\n    &.more {\n      background-color: #8c8c8c;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #a6a6a6;\n      }\n    }\n\n    &.settings {\n      background-color: #722ed1;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3crect x='3' y='12' width='4' height='9'/%3e%3crect x='10' y='8' width='4' height='13'/%3e%3crect x='17' y='4' width='4' height='17'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #9254de;\n      }\n    }\n\n    &.close {\n      background-color: #ff4d4f;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M6 18L18 6M6 6l12 12'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #ff7875;\n      }\n    }\n  }\n\n  .chart-status {\n    font-size: 12px;\n    color: #8c8c8c;\n    margin-left: 8px;\n  }\n}\n\n.chart-content {\n  padding: 20px;\n}\n\n.chart-legend {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  margin-bottom: 16px;\n  font-size: 12px;\n\n  .legend-item {\n    display: flex;\n    align-items: center;\n    gap: 6px;\n\n    .legend-color {\n      width: 12px;\n      height: 12px;\n      border-radius: 2px;\n\n      &.blue {\n        background: #5B8FF9;\n      }\n\n      &.yellow {\n        background: #FFD666;\n      }\n\n      &.line {\n        background: #FF6B6B;\n        border-radius: 50%;\n        width: 8px;\n        height: 8px;\n      }\n    }\n  }\n}\n\n.chart-wrapper {\n  width: 100%;\n  height: 300px;\n}\n\n.chart {\n  width: 100%;\n  height: 100%;\n}\n\n.value-content {\n  padding: 20px;\n}\n\n.value-main {\n  .value-label {\n    font-size: 12px;\n    color: #8c8c8c;\n    margin-bottom: 8px;\n    display: block;\n  }\n\n  .value-number {\n    font-size: 36px;\n    font-weight: bold;\n    color: #262626;\n    line-height: 1;\n    margin-bottom: 12px;\n\n    .value-unit {\n      font-size: 18px;\n      color: #8c8c8c;\n      margin-left: 4px;\n    }\n  }\n\n  .value-change {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    font-size: 12px;\n\n    .change-text {\n      color: #8c8c8c;\n    }\n\n    .change-value {\n      &.positive {\n        color: #52c41a;\n      }\n\n      &.negative {\n        color: #ff4d4f;\n      }\n    }\n\n    .change-arrow {\n      width: 0;\n      height: 0;\n\n      &.up {\n        border-left: 4px solid transparent;\n        border-right: 4px solid transparent;\n        border-bottom: 6px solid #52c41a;\n      }\n\n      &.down {\n        border-left: 4px solid transparent;\n        border-right: 4px solid transparent;\n        border-top: 6px solid #ff4d4f;\n      }\n    }\n  }\n}\n\n.control-panel {\n  position: fixed;\n  right: 20px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 280px;\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n  z-index: 1000;\n\n  .panel-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 16px 20px;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafbfc;\n    border-radius: 8px 8px 0 0;\n\n    span {\n      font-size: 14px;\n      font-weight: 500;\n      color: #262626;\n    }\n\n    .panel-close {\n      cursor: pointer;\n      font-size: 16px;\n      color: #8c8c8c;\n    }\n  }\n\n  .panel-content {\n    padding: 20px;\n\n    .panel-section {\n      h4 {\n        margin: 0 0 12px 0;\n        font-size: 14px;\n        color: #262626;\n      }\n\n      .setting-item {\n        margin-bottom: 16px;\n\n        label {\n          display: block;\n          margin-bottom: 6px;\n          font-size: 12px;\n          color: #8c8c8c;\n        }\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 1200px) {\n  .search-container {\n    .search-form {\n      .form-row {\n        gap: 12px;\n      }\n\n      .form-group {\n        .form-input.keyword-input {\n          width: 160px;\n        }\n      }\n\n      .form-buttons {\n        margin-left: 0;\n        margin-top: 8px;\n        width: 100%;\n        justify-content: flex-start;\n      }\n    }\n  }\n\n  .top-section, .bottom-section {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .assistant-panel {\n    width: 100%;\n    order: -1; // 智能助手面板在移动端显示在图表上方\n  }\n}\n\n@media (max-width: 768px) {\n  .dashboard-container {\n    padding: 10px;\n    gap: 15px;\n  }\n\n  .search-container {\n    .search-form {\n      .form-row {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 12px;\n      }\n\n      .form-group {\n        width: 100%;\n\n        .form-input {\n          flex: 1;\n          min-width: 120px;\n\n          &.keyword-input {\n            width: 100%;\n          }\n        }\n\n        .form-select {\n          flex: 1;\n          min-width: 120px;\n        }\n      }\n\n      .form-buttons {\n        width: 100%;\n        justify-content: center;\n        margin-top: 12px;\n      }\n    }\n  }\n\n  .top-section, .bottom-section {\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .assistant-panel {\n    width: 100%;\n    order: -1;\n\n    .panel-content {\n      padding: 15px;\n\n      .assistant-item {\n        padding: 10px 0;\n\n        .assistant-icon {\n          width: 28px;\n          height: 28px;\n          font-size: 16px;\n        }\n\n        .assistant-text {\n          font-size: 13px;\n        }\n      }\n    }\n  }\n\n  .chart-header, .value-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n    padding: 12px 16px;\n  }\n\n  .chart-meta, .value-meta {\n    order: 1;\n  }\n\n  .chart-actions, .value-actions {\n    order: 2;\n    align-self: flex-end;\n  }\n\n  .chart-content {\n    padding: 16px;\n  }\n\n  .value-content {\n    padding: 16px;\n  }\n\n  .chart-wrapper {\n    height: 250px;\n  }\n\n  .value-number {\n    font-size: 28px !important;\n\n    .value-unit {\n      font-size: 14px !important;\n    }\n  }\n}\n\n// 更多操作弹窗样式\n.more-popup {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  min-width: 160px;\n  overflow: hidden;\n  position: fixed;\n  z-index: 2000;\n\n  .more-popup-content {\n    .more-action-item {\n      padding: 12px 16px;\n      font-size: 14px;\n      color: #262626;\n      cursor: pointer;\n      transition: all 0.2s;\n      border-bottom: 1px solid #f5f5f5;\n      line-height: 1.4;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:hover {\n        background: #f0f8ff;\n        color: #1890ff;\n      }\n\n      &:active {\n        background: #e6f7ff;\n        color: #1890ff;\n      }\n    }\n  }\n}\n\n// 分享弹窗样式\n.share-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.share-popup {\n  background: white;\n  border-radius: 8px;\n  width: 400px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n}\n\n.share-popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.share-popup-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.share-popup-close {\n  background: none;\n  border: none;\n  font-size: 20px;\n  color: #999;\n  cursor: pointer;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: #666;\n  }\n}\n\n.share-popup-content {\n  padding: 20px;\n}\n\n.share-description {\n  color: #666;\n  font-size: 14px;\n  margin-bottom: 20px;\n  line-height: 1.5;\n}\n\n.share-option {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.share-option-label {\n  font-size: 14px;\n  color: #333;\n}\n\n.share-toggle {\n  position: relative;\n}\n\n.toggle-input {\n  display: none;\n}\n\n.toggle-label {\n  display: block;\n  width: 44px;\n  height: 24px;\n  background: #ddd;\n  border-radius: 12px;\n  cursor: pointer;\n  position: relative;\n  transition: background 0.3s;\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 2px;\n    left: 2px;\n    width: 20px;\n    height: 20px;\n    background: white;\n    border-radius: 50%;\n    transition: transform 0.3s;\n  }\n}\n\n.toggle-input:checked + .toggle-label {\n  background: #1890ff;\n\n  &::after {\n    transform: translateX(20px);\n  }\n}\n\n.share-link-section {\n  display: flex;\n  gap: 8px;\n}\n\n.share-link-input {\n  flex: 1;\n  height: 36px;\n  padding: 0 12px;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  font-size: 14px;\n  color: #666;\n  background: #f5f5f5;\n  outline: none;\n}\n\n.copy-link-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: #1890ff;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-size: 14px;\n  cursor: pointer;\n  transition: background 0.3s;\n\n  &:hover {\n    background: #40a9ff;\n  }\n\n  &:active {\n    background: #096dd9;\n  }\n}\n\n// 卡片提醒弹窗样式\n.reminder-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 10000 !important;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.reminder-popup {\n  background: white;\n  border-radius: 8px;\n  width: 520px;\n  max-width: 90vw;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n  z-index: 10001 !important;\n  position: relative;\n}\n\n.reminder-popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.reminder-popup-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.reminder-popup-close {\n  background: none;\n  border: none;\n  font-size: 20px;\n  color: #999;\n  cursor: pointer;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: #666;\n  }\n}\n\n.reminder-popup-content {\n  padding: 20px;\n}\n\n.reminder-form-item {\n  margin-bottom: 16px;\n\n  .reminder-label {\n    display: block;\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n\n  .reminder-select {\n    width: 100%;\n    height: 36px;\n    padding: 0 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n    cursor: pointer;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n  }\n\n  .reminder-input {\n    width: 100%;\n    height: 36px;\n    padding: 0 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n  }\n\n  .reminder-change-section {\n    display: flex;\n    gap: 12px;\n\n    .reminder-select-small {\n      flex: 1;\n      height: 36px;\n      padding: 0 12px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 14px;\n      color: #333;\n      background: white;\n      outline: none;\n      cursor: pointer;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n      }\n    }\n  }\n\n  .reminder-threshold-section {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .reminder-number-input {\n      width: 120px;\n      height: 36px;\n      padding: 0 12px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 14px;\n      color: #333;\n      background: white;\n      outline: none;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n      }\n    }\n\n    .reminder-unit {\n      font-size: 14px;\n      color: #666;\n    }\n\n    .reminder-checkbox-section {\n      display: flex;\n      align-items: center;\n      gap: 6px;\n      margin-left: auto;\n\n      .reminder-checkbox {\n        width: 16px;\n        height: 16px;\n        cursor: pointer;\n      }\n\n      .reminder-checkbox-label {\n        font-size: 14px;\n        color: #333;\n        cursor: pointer;\n        margin: 0;\n      }\n    }\n  }\n\n  .reminder-method-section {\n    display: flex;\n    gap: 20px;\n\n    .reminder-radio-item {\n      display: flex;\n      align-items: center;\n      gap: 6px;\n\n      .reminder-radio {\n        width: 16px;\n        height: 16px;\n        cursor: pointer;\n      }\n\n      .reminder-radio-label {\n        font-size: 14px;\n        color: #333;\n        cursor: pointer;\n        margin: 0;\n      }\n    }\n  }\n}\n\n.reminder-description {\n  font-size: 13px;\n  color: #999;\n  margin: 16px 0;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  line-height: 1.5;\n}\n\n.reminder-popup-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  padding: 16px 20px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.reminder-cancel-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: white;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  color: #666;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    color: #1890ff;\n    border-color: #1890ff;\n  }\n}\n\n.reminder-confirm-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: #1890ff;\n  border: none;\n  border-radius: 4px;\n  color: white;\n  font-size: 14px;\n  cursor: pointer;\n  transition: background 0.3s;\n\n  &:hover {\n    background: #40a9ff;\n  }\n\n  &:active {\n    background: #096dd9;\n  }\n}\n\n// 上传CSV弹窗样式\n.upload-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-popup {\n  background: white;\n  border-radius: 8px;\n  width: 480px;\n  max-width: 90vw;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n}\n\n.upload-popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.upload-popup-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.upload-popup-close {\n  background: none;\n  border: none;\n  font-size: 20px;\n  color: #999;\n  cursor: pointer;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: #666;\n  }\n}\n\n.upload-popup-content {\n  padding: 20px;\n}\n\n.upload-form-item {\n  margin-bottom: 16px;\n\n  .upload-label {\n    display: block;\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n\n  .upload-input {\n    width: 100%;\n    height: 36px;\n    padding: 0 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n  }\n\n  .upload-textarea {\n    width: 100%;\n    padding: 8px 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n    resize: vertical;\n    min-height: 80px;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n  }\n\n  .upload-file-section {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .upload-file-input {\n      display: none;\n    }\n\n    .upload-file-button {\n      height: 36px;\n      padding: 0 16px;\n      background: #1890ff;\n      border: none;\n      border-radius: 4px;\n      color: white;\n      font-size: 14px;\n      cursor: pointer;\n      transition: background 0.3s;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      &:hover {\n        background: #40a9ff;\n      }\n\n      &:active {\n        background: #096dd9;\n      }\n    }\n\n    .upload-file-name {\n      font-size: 14px;\n      color: #333;\n      flex: 1;\n    }\n\n    .upload-file-placeholder {\n      font-size: 14px;\n      color: #bfbfbf;\n      flex: 1;\n    }\n  }\n}\n\n.upload-tips {\n  margin-top: 20px;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  border-left: 3px solid #1890ff;\n\n  .upload-tips-title {\n    font-size: 14px;\n    font-weight: 500;\n    color: #333;\n    margin-bottom: 8px;\n  }\n\n  .upload-tips-content {\n    font-size: 13px;\n    color: #666;\n    line-height: 1.6;\n  }\n}\n\n.upload-popup-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  padding: 16px 20px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.upload-cancel-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: white;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  color: #666;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    color: #1890ff;\n    border-color: #1890ff;\n  }\n}\n\n.upload-confirm-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: #1890ff;\n  border: none;\n  border-radius: 4px;\n  color: white;\n  font-size: 14px;\n  cursor: pointer;\n  transition: background 0.3s;\n\n  &:hover {\n    background: #40a9ff;\n  }\n\n  &:active {\n    background: #096dd9;\n  }\n}\n\n// 设置弹窗样式\n.settings-popup {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  padding: 8px;\n  overflow: hidden;\n\n  .settings-popup-content {\n    .chart-types-grid {\n      display: grid;\n      grid-template-columns: repeat(4, 1fr);\n      gap: 8px;\n\n      .chart-type-item {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 8px 4px;\n        cursor: pointer;\n        transition: all 0.2s;\n        border-radius: 4px;\n        min-height: 60px;\n\n        &:hover {\n          background: #f0f8ff;\n          transform: translateY(-2px);\n        }\n\n        &:active {\n          background: #e6f7ff;\n          transform: translateY(0);\n        }\n\n        .chart-icon {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-bottom: 4px;\n\n          svg {\n            width: 20px;\n            height: 20px;\n            transition: all 0.2s;\n          }\n        }\n\n        .chart-label {\n          font-size: 12px;\n          color: #262626;\n          text-align: center;\n          line-height: 1.2;\n          white-space: nowrap;\n        }\n\n        &:hover {\n          .chart-icon svg {\n            transform: scale(1.1);\n          }\n\n          .chart-label {\n            color: #1890ff;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}