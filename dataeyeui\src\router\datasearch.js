import Layout from '@/layout'

const datasearchRouter = {
  path: '/datasearch',
  component: Layout,
  redirect: '/datasearch/index',
  name: 'DataSearch',
  meta: {
    title: '数据搜索',
    icon: 'search'
  },
  children: [
    {
      path: 'index',
      component: () => import('@/views/datasearch/index'),
      name: 'DataSearchIndex',
      meta: {
        title: '数据搜索',
        icon: 'search',
        affix: false
      }
    }
  ]
}

export default datasearchRouter
