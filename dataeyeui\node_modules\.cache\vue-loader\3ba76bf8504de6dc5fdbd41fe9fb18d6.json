{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\src\\views\\datasearch\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\src\\views\\datasearch\\index.vue", "mtime": 1748243803734}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748221550136}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1748221552819}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1748221550136}, {"path": "C:\\Users\\<USER>\\Desktop\\cctv\\dataeyeui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1748221554057}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCAqIGFzIGVjaGFydHMgZnJvbSAnZWNoYXJ0cyc7CgpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogIkRhdGFTZWFyY2giLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDmkJzntKLooajljZXmlbDmja4KICAgICAgc2VhcmNoRm9ybTogewogICAgICAgIGtleXdvcmQ6ICfpl6jlupcg6JCl5Lia6aKdIOWJjeWNgSDpl6jlupcg6JCl5Lia6aKdJywKICAgICAgICBkYXRhVHlwZTogJ3N0b3JlJywKICAgICAgICBzdG9yZTogJ2FsbCcsCiAgICAgICAgdGltZTogJzIwMjQnCiAgICAgIH0sCiAgICAgIC8vIOetm+mAieW8ueeql+ebuOWFswogICAgICBzaG93RmlsdGVyUG9wdXA6IGZhbHNlLAogICAgICBmaWx0ZXJQb3B1cFN0eWxlOiB7fSwKICAgICAgZmlsdGVyU2VhcmNoUXVlcnk6ICcnLAogICAgICBhY3RpdmVUYWI6ICfnu7TluqYnLAogICAgICAvLyDmm7TlpJrmk43kvZzlvLnnqpcKICAgICAgc2hvd01vcmVQb3B1cDogZmFsc2UsCiAgICAgIG1vcmVQb3B1cFN0eWxlOiB7fSwKICAgICAgLy8g5YiG5Lqr5by556qX55u45YWzCiAgICAgIHNob3dTaGFyZVBvcHVwOiBmYWxzZSwKICAgICAgZW1iZWRFbmFibGVkOiB0cnVlLAogICAgICBzaGFyZUxpbms6ICdodHRwczovL2R3ei5jbi9qendNZE1oJywKCiAgICAgIC8vIOWNoeeJh+aPkOmGkuW8ueeqlwogICAgICBzaG93UmVtaW5kZXJQb3B1cDogZmFsc2UsCiAgICAgIHJlbWluZGVyRm9ybTogewogICAgICAgIGNhcmROYW1lOiAnJywKICAgICAgICBlbWFpbDogJycsCiAgICAgICAgY2hhbmdlVHlwZTogJ+WQjOavlOWinuWHj+W5hScsCiAgICAgICAgdGltZVBlcmlvZDogJ+WkqeaVsCcsCiAgICAgICAgdGhyZXNob2xkOiAwLAogICAgICAgIGNvbnRlbnRDaGFuZ2U6IGZhbHNlLAogICAgICAgIG1ldGhvZDogJ2VtYWlsJwogICAgICB9LAogICAgICAvLyDkuIrkvKBDU1blvLnnqpcKICAgICAgc2hvd1VwbG9hZFBvcHVwOiBmYWxzZSwKICAgICAgdXBsb2FkRm9ybTogewogICAgICAgIHJlcG9ydE5hbWU6ICcnLAogICAgICAgIGRlc2NyaXB0aW9uOiAnJywKICAgICAgICBmaWxlOiBudWxsCiAgICAgIH0sCiAgICAgIC8vIOWbvuihqOmAieaLqeW8ueeqlwogICAgICBzaG93Q2hhcnRTZWxlY3RvcjogZmFsc2UsCiAgICAgIHNlbGVjdGVkQ2hhcnRUeXBlOiAnYmFyJywKICAgICAgLy8g6K6+572u5by556qXCiAgICAgIHNob3dTZXR0aW5nc1BvcHVwOiBmYWxzZSwKICAgICAgc2V0dGluZ3NQb3B1cFN0eWxlOiB7fSwKICAgICAgLy8g5Zu+6KGo5a6e5L6LCiAgICAgIHN0b3JlUmV2ZW51ZUNoYXJ0OiBudWxsLAogICAgICBjbG91ZFJldmVudWVDaGFydDogbnVsbCwKICAgICAgLy8g5Yi35paw6Ze06ZqUCiAgICAgIHJlZnJlc2hJbnRlcnZhbDogJzAnLAogICAgICAvLyDpl6jlupfokKXkuJrpop3mlbDmja4KICAgICAgc3RvcmVSZXZlbnVlRGF0YTogewogICAgICAgIGNhdGVnb3JpZXM6IFsn5rex5ZyzJywgJ+W5v+W3nicsICfljJfkuqwnLCAn5LiK5rW3JywgJ+adreW3nicsICfljZfkuqwnLCAn5oiQ6YO9JywgJ+mHjeW6hicsICfmrabmsYknLCAn6KW/5a6JJywgJ+Wkqea0pSddLAogICAgICAgIHJldmVudWU6IFsyMTM0MCwgMTYyMDAsIDE0MTAwLCA4MTAwLCA3NjEwLCA2MjAwLCA1MzEwLCA0ODYwLCA0MzQwLCAzNDYwLCAzMTQwXSwKICAgICAgICBwcm9maXQ6IFsyMjQxMCwgMTg5NDAsIDE0MjAwLCAxMjQwMCwgNzYwMCwgNjQyMCwgNTQwMCwgNDc0MCwgNDM2MCwgMzc0MCwgMzE0MF0sCiAgICAgICAgZ3Jvd3RoUmF0ZTogWzExLjM5LCA5LjA0LCA4LjMxLCA3LjYwLCA1LjM3LCA1LjA0LCA0Ljc0LCA0LjM0LCA0LjE3LCAzLjg2LCAzLjcwXQogICAgICB9LAogICAgICAvLyDkupHokKXkuJrpop3mlbDmja4KICAgICAgY2xvdWRSZXZlbnVlRGF0YTogewogICAgICAgIGNhdGVnb3JpZXM6IFsn5Y2O5Y2X5aSn5Yy6X+a3seWcsycsICfljY7ljZflpKfljLpf5bm/5beeJywgJ+WNjuS4nOWkp+WMul/kuIrmtbcnLCAn5Y2O5Lic5aSn5Yy6X+adreW3nicsICfljY7ljJflpKfljLpf5YyX5LqsJywgJ+WNjuS4reWkp+WMul/mrabmsYknLCAn6KW/5Y2X5aSn5Yy6X+aIkOmDvScsICfopb/ljZflpKfljLpf6YeN5bqGJywgJ+ilv+WMl+Wkp+WMul/opb/lroknLCAn5Y2O5YyX5aSn5Yy6X+Wkqea0pScsICfljY7kuJzlpKfljLpf5Y2X5LqsJ10sCiAgICAgICAgcmV2ZW51ZTogWzYwMDAwMDAsIDU4MDAwMDAsIDQxMDAwMDAsIDQxMDAwMDAsIDM0MDAwMDAsIDI2MDAwMDAsIDI0MDAwMDAsIDIxMDAwMDAsIDIwMDAwMDAsIDE5MDAwMDAsIDE4MDAwMDBdLAogICAgICAgIHByb2ZpdDogWzU4MDAwMDAsIDU2MDAwMDAsIDQxMDAwMDAsIDQxMDAwMDAsIDMyMDAwMDAsIDI0MDAwMDAsIDIyMDAwMDAsIDIwMDAwMDAsIDE5MDAwMDAsIDE4MDAwMDAsIDE3MDAwMDBdLAogICAgICAgIGdyb3d0aFJhdGU6IFs0LjcwLCAtMC4yLCAtNi4zLCAtNi4zLCAxLjksIDIuNiwgMi43LCAyLjEsIDIuMCwgMS45LCAxLjhdCiAgICAgIH0KICAgIH07CiAgfSwKICBtb3VudGVkKCkgewogICAgdGhpcy5pbml0Q2hhcnRzKCk7CiAgICAvLyDmt7vliqDngrnlh7vlpJbpg6jlhbPpl63lvLnnqpfnmoTkuovku7bnm5HlkKwKICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgdGhpcy5oYW5kbGVDbGlja091dHNpZGUpOwogIH0sCiAgYmVmb3JlRGVzdHJveSgpIHsKICAgIGlmICh0aGlzLnN0b3JlUmV2ZW51ZUNoYXJ0KSB7CiAgICAgIHRoaXMuc3RvcmVSZXZlbnVlQ2hhcnQuZGlzcG9zZSgpOwogICAgfQogICAgaWYgKHRoaXMuY2xvdWRSZXZlbnVlQ2hhcnQpIHsKICAgICAgdGhpcy5jbG91ZFJldmVudWVDaGFydC5kaXNwb3NlKCk7CiAgICB9CiAgICAvLyDnp7vpmaTkuovku7bnm5HlkKwKICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgdGhpcy5oYW5kbGVDbGlja091dHNpZGUpOwogIH0sCiAgbWV0aG9kczogewogICAgLyoqIOWIneWni+WMluWbvuihqCAqLwogICAgaW5pdENoYXJ0cygpIHsKICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gewogICAgICAgIHRoaXMuaW5pdFN0b3JlUmV2ZW51ZUNoYXJ0KCk7CiAgICAgICAgdGhpcy5pbml0Q2xvdWRSZXZlbnVlQ2hhcnQoKTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8qKiDliJ3lp4vljJbpl6jlupfokKXkuJrpop3lm77ooaggKi8KICAgIGluaXRTdG9yZVJldmVudWVDaGFydCgpIHsKICAgICAgdGhpcy5zdG9yZVJldmVudWVDaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLnN0b3JlUmV2ZW51ZUNoYXJ0KTsKCiAgICAgIGNvbnN0IG9wdGlvbiA9IHsKICAgICAgICB0b29sdGlwOiB7CiAgICAgICAgICB0cmlnZ2VyOiAnYXhpcycsCiAgICAgICAgICBheGlzUG9pbnRlcjogewogICAgICAgICAgICB0eXBlOiAnY3Jvc3MnLAogICAgICAgICAgICBjcm9zc1N0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6ICcjOTk5JwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgfSwKICAgICAgICBsZWdlbmQ6IHsKICAgICAgICAgIHNob3c6IGZhbHNlCiAgICAgICAgfSwKICAgICAgICB4QXhpczogWwogICAgICAgICAgewogICAgICAgICAgICB0eXBlOiAnY2F0ZWdvcnknLAogICAgICAgICAgICBkYXRhOiB0aGlzLnN0b3JlUmV2ZW51ZURhdGEuY2F0ZWdvcmllcywKICAgICAgICAgICAgYXhpc1BvaW50ZXI6IHsKICAgICAgICAgICAgICB0eXBlOiAnc2hhZG93JwogICAgICAgICAgICB9LAogICAgICAgICAgICBheGlzTGFiZWw6IHsKICAgICAgICAgICAgICByb3RhdGU6IDQ1LAogICAgICAgICAgICAgIGZvbnRTaXplOiAxMAogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgXSwKICAgICAgICB5QXhpczogWwogICAgICAgICAgewogICAgICAgICAgICB0eXBlOiAndmFsdWUnLAogICAgICAgICAgICBuYW1lOiAn6JCl5Lia6aKdKOS4h+WFgyknLAogICAgICAgICAgICBwb3NpdGlvbjogJ2xlZnQnLAogICAgICAgICAgICBheGlzTGFiZWw6IHsKICAgICAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uKHZhbHVlKSB7CiAgICAgICAgICAgICAgICBpZiAodmFsdWUgPj0gMTAwMDApIHsKICAgICAgICAgICAgICAgICAgcmV0dXJuICh2YWx1ZSAvIDEwMDAwKS50b0ZpeGVkKDEpICsgJ+S4hyc7CiAgICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWU7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICB0eXBlOiAndmFsdWUnLAogICAgICAgICAgICBuYW1lOiAn6JCl5Lia6aKd5ZCM5q+U5aKe6ZW/546HJywKICAgICAgICAgICAgcG9zaXRpb246ICdyaWdodCcsCiAgICAgICAgICAgIGF4aXNMYWJlbDogewogICAgICAgICAgICAgIGZvcm1hdHRlcjogJ3t2YWx1ZX0lJwogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgXSwKICAgICAgICBzZXJpZXM6IFsKICAgICAgICAgIHsKICAgICAgICAgICAgbmFtZTogJ+iQpeS4muminS/kuIflhYMnLAogICAgICAgICAgICB0eXBlOiAnYmFyJywKICAgICAgICAgICAgZGF0YTogdGhpcy5zdG9yZVJldmVudWVEYXRhLnJldmVudWUsCiAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnIzVCOEZGOScKICAgICAgICAgICAgfSwKICAgICAgICAgICAgYmFyV2lkdGg6ICcyMCUnLAogICAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICAgIHNob3c6IHRydWUsCiAgICAgICAgICAgICAgcG9zaXRpb246ICd0b3AnLAogICAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24ocGFyYW1zKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gKHBhcmFtcy52YWx1ZSAvIDEwMDAwKS50b0ZpeGVkKDEpICsgJ+S4hyc7CiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICBmb250U2l6ZTogMTAKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbmFtZTogJ+WIqea2pi/kuIflhYMnLAogICAgICAgICAgICB0eXBlOiAnYmFyJywKICAgICAgICAgICAgZGF0YTogdGhpcy5zdG9yZVJldmVudWVEYXRhLnByb2ZpdCwKICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6ICcjRkZENjY2JwogICAgICAgICAgICB9LAogICAgICAgICAgICBiYXJXaWR0aDogJzIwJScsCiAgICAgICAgICAgIGxhYmVsOiB7CiAgICAgICAgICAgICAgc2hvdzogdHJ1ZSwKICAgICAgICAgICAgICBwb3NpdGlvbjogJ3RvcCcsCiAgICAgICAgICAgICAgZm9ybWF0dGVyOiBmdW5jdGlvbihwYXJhbXMpIHsKICAgICAgICAgICAgICAgIHJldHVybiAocGFyYW1zLnZhbHVlIC8gMTAwMDApLnRvRml4ZWQoMSkgKyAn5LiHJzsKICAgICAgICAgICAgICB9LAogICAgICAgICAgICAgIGZvbnRTaXplOiAxMAogICAgICAgICAgICB9CiAgICAgICAgICB9LAogICAgICAgICAgewogICAgICAgICAgICBuYW1lOiAn6JCl5Lia6aKd5ZCM5q+U5aKe6ZW/546HJywKICAgICAgICAgICAgdHlwZTogJ2xpbmUnLAogICAgICAgICAgICB5QXhpc0luZGV4OiAxLAogICAgICAgICAgICBkYXRhOiB0aGlzLnN0b3JlUmV2ZW51ZURhdGEuZ3Jvd3RoUmF0ZSwKICAgICAgICAgICAgaXRlbVN0eWxlOiB7CiAgICAgICAgICAgICAgY29sb3I6ICcjRkY2QjZCJwogICAgICAgICAgICB9LAogICAgICAgICAgICBsaW5lU3R5bGU6IHsKICAgICAgICAgICAgICB3aWR0aDogMgogICAgICAgICAgICB9LAogICAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICAgIHNob3c6IHRydWUsCiAgICAgICAgICAgICAgcG9zaXRpb246ICd0b3AnLAogICAgICAgICAgICAgIGZvcm1hdHRlcjogJ3tjfSUnLAogICAgICAgICAgICAgIGZvbnRTaXplOiAxMAogICAgICAgICAgICB9CiAgICAgICAgICB9CiAgICAgICAgXQogICAgICB9OwoKICAgICAgdGhpcy5zdG9yZVJldmVudWVDaGFydC5zZXRPcHRpb24ob3B0aW9uKTsKICAgIH0sCgogICAgLyoqIOWIneWni+WMluS6keiQpeS4mumineWbvuihqCAqLwogICAgaW5pdENsb3VkUmV2ZW51ZUNoYXJ0KCkgewogICAgICB0aGlzLmNsb3VkUmV2ZW51ZUNoYXJ0ID0gZWNoYXJ0cy5pbml0KHRoaXMuJHJlZnMuY2xvdWRSZXZlbnVlQ2hhcnQpOwoKICAgICAgY29uc3Qgb3B0aW9uID0gewogICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgIHRyaWdnZXI6ICdheGlzJywKICAgICAgICAgIGF4aXNQb2ludGVyOiB7CiAgICAgICAgICAgIHR5cGU6ICdjcm9zcycsCiAgICAgICAgICAgIGNyb3NzU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJyM5OTknCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9LAogICAgICAgIGxlZ2VuZDogewogICAgICAgICAgc2hvdzogZmFsc2UKICAgICAgICB9LAogICAgICAgIHhBeGlzOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHR5cGU6ICdjYXRlZ29yeScsCiAgICAgICAgICAgIGRhdGE6IHRoaXMuY2xvdWRSZXZlbnVlRGF0YS5jYXRlZ29yaWVzLAogICAgICAgICAgICBheGlzUG9pbnRlcjogewogICAgICAgICAgICAgIHR5cGU6ICdzaGFkb3cnCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGF4aXNMYWJlbDogewogICAgICAgICAgICAgIHJvdGF0ZTogNDUsCiAgICAgICAgICAgICAgZm9udFNpemU6IDEwCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICBdLAogICAgICAgIHlBeGlzOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHR5cGU6ICd2YWx1ZScsCiAgICAgICAgICAgIG5hbWU6ICfokKXkuJrpop0o5LiH5YWDKScsCiAgICAgICAgICAgIHBvc2l0aW9uOiAnbGVmdCcsCiAgICAgICAgICAgIGF4aXNMYWJlbDogewogICAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24odmFsdWUpIHsKICAgICAgICAgICAgICAgIHJldHVybiAodmFsdWUgLyAxMDAwMCkudG9GaXhlZCgwKSArICfkuIcnOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgdHlwZTogJ3ZhbHVlJywKICAgICAgICAgICAgbmFtZTogJ+iQpeS4mumineWQjOavlOWinumVv+eOhycsCiAgICAgICAgICAgIHBvc2l0aW9uOiAncmlnaHQnLAogICAgICAgICAgICBheGlzTGFiZWw6IHsKICAgICAgICAgICAgICBmb3JtYXR0ZXI6ICd7dmFsdWV9JScKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIF0sCiAgICAgICAgc2VyaWVzOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIG5hbWU6ICfokKXkuJrpop0v5LiH5YWDJywKICAgICAgICAgICAgdHlwZTogJ2JhcicsCiAgICAgICAgICAgIGRhdGE6IHRoaXMuY2xvdWRSZXZlbnVlRGF0YS5yZXZlbnVlLAogICAgICAgICAgICBpdGVtU3R5bGU6IHsKICAgICAgICAgICAgICBjb2xvcjogJyM1QjhGRjknCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIGJhcldpZHRoOiAnMjAlJywKICAgICAgICAgICAgbGFiZWw6IHsKICAgICAgICAgICAgICBzaG93OiB0cnVlLAogICAgICAgICAgICAgIHBvc2l0aW9uOiAndG9wJywKICAgICAgICAgICAgICBmb3JtYXR0ZXI6IGZ1bmN0aW9uKHBhcmFtcykgewogICAgICAgICAgICAgICAgcmV0dXJuIChwYXJhbXMudmFsdWUgLyAxMDAwMCkudG9GaXhlZCgwKSArICfkuIcnOwogICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgZm9udFNpemU6IDEwCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sCiAgICAgICAgICB7CiAgICAgICAgICAgIG5hbWU6ICfliKnmtqYv5LiH5YWDJywKICAgICAgICAgICAgdHlwZTogJ2JhcicsCiAgICAgICAgICAgIGRhdGE6IHRoaXMuY2xvdWRSZXZlbnVlRGF0YS5wcm9maXQsCiAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnI0ZGRDY2NicKICAgICAgICAgICAgfSwKICAgICAgICAgICAgYmFyV2lkdGg6ICcyMCUnLAogICAgICAgICAgICBsYWJlbDogewogICAgICAgICAgICAgIHNob3c6IHRydWUsCiAgICAgICAgICAgICAgcG9zaXRpb246ICd0b3AnLAogICAgICAgICAgICAgIGZvcm1hdHRlcjogZnVuY3Rpb24ocGFyYW1zKSB7CiAgICAgICAgICAgICAgICByZXR1cm4gKHBhcmFtcy52YWx1ZSAvIDEwMDAwKS50b0ZpeGVkKDApICsgJ+S4hyc7CiAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICBmb250U2l6ZTogMTAKICAgICAgICAgICAgfQogICAgICAgICAgfSwKICAgICAgICAgIHsKICAgICAgICAgICAgbmFtZTogJ+iQpeS4mumineWQjOavlOWinumVv+eOhycsCiAgICAgICAgICAgIHR5cGU6ICdsaW5lJywKICAgICAgICAgICAgeUF4aXNJbmRleDogMSwKICAgICAgICAgICAgZGF0YTogdGhpcy5jbG91ZFJldmVudWVEYXRhLmdyb3d0aFJhdGUsCiAgICAgICAgICAgIGl0ZW1TdHlsZTogewogICAgICAgICAgICAgIGNvbG9yOiAnI0ZGNkI2QicKICAgICAgICAgICAgfSwKICAgICAgICAgICAgbGluZVN0eWxlOiB7CiAgICAgICAgICAgICAgd2lkdGg6IDIKICAgICAgICAgICAgfSwKICAgICAgICAgICAgbGFiZWw6IHsKICAgICAgICAgICAgICBzaG93OiB0cnVlLAogICAgICAgICAgICAgIHBvc2l0aW9uOiAndG9wJywKICAgICAgICAgICAgICBmb3JtYXR0ZXI6ICd7Y30lJywKICAgICAgICAgICAgICBmb250U2l6ZTogMTAKICAgICAgICAgICAgfQogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfTsKCiAgICAgIHRoaXMuY2xvdWRSZXZlbnVlQ2hhcnQuc2V0T3B0aW9uKG9wdGlvbik7CiAgICB9LAoKICAgIC8vIOaQnOe0ouagj+Wbvuagh+aMiemSruaWueazlQogICAgaGFuZGxlQ2xvc2UoKSB7CiAgICAgIGNvbnNvbGUubG9nKCflhbPpl63mkJzntKInKTsKICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCflhbPpl63mkJzntKInKTsKICAgIH0sCgogICAgaGFuZGxlU2VhcmNoKCkgewogICAgICBjb25zb2xlLmxvZygn5omn6KGM5pCc57SiJyk7CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5pCc57Si5Yqf6IO95byA5Y+R5LitLi4uJyk7CiAgICB9LAoKICAgIGhhbmRsZU1pbnVzKCkgewogICAgICBjb25zb2xlLmxvZygn5YeP5Y+35pON5L2cJyk7CiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbygn5YeP5Y+35Yqf6IO95byA5Y+R5LitLi4uJyk7CiAgICB9LAoKICAgIGhhbmRsZUZpbHRlcigpIHsKICAgICAgY29uc29sZS5sb2coJ+etm+mAieWKn+iDvScpOwogICAgICB0aGlzLnNob3dGaWx0ZXJQb3B1cCA9ICF0aGlzLnNob3dGaWx0ZXJQb3B1cDsKCiAgICAgIGlmICh0aGlzLnNob3dGaWx0ZXJQb3B1cCkgewogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsKICAgICAgICAgIHRoaXMuc2V0RmlsdGVyUG9wdXBQb3NpdGlvbigpOwogICAgICAgIH0pOwogICAgICB9CiAgICB9LAoKICAgIC8vIOiuvue9ruW8ueeql+S9jee9rgogICAgc2V0RmlsdGVyUG9wdXBQb3NpdGlvbigpIHsKICAgICAgY29uc3QgZmlsdGVyQnV0dG9uID0gdGhpcy4kcmVmcy5maWx0ZXJCdXR0b247CiAgICAgIGlmIChmaWx0ZXJCdXR0b24pIHsKICAgICAgICBjb25zdCByZWN0ID0gZmlsdGVyQnV0dG9uLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpOwogICAgICAgIHRoaXMuZmlsdGVyUG9wdXBTdHlsZSA9IHsKICAgICAgICAgIHBvc2l0aW9uOiAnZml4ZWQnLAogICAgICAgICAgdG9wOiByZWN0LmJvdHRvbSArIDUgKyAncHgnLAogICAgICAgICAgcmlnaHQ6ICh3aW5kb3cuaW5uZXJXaWR0aCAtIHJlY3QucmlnaHQpICsgJ3B4JywKICAgICAgICAgIHpJbmRleDogMTAwMAogICAgICAgIH07CiAgICAgIH0KICAgIH0sCgogICAgLy8g5YWz6Zet562b6YCJ5by556qXCiAgICBjbG9zZUZpbHRlclBvcHVwKCkgewogICAgICB0aGlzLnNob3dGaWx0ZXJQb3B1cCA9IGZhbHNlOwogICAgfSwKCiAgICAvLyDpgInmi6nnrZvpgInpobkKICAgIHNlbGVjdEZpbHRlcihpdGVtKSB7CiAgICAgIGNvbnNvbGUubG9nKCfpgInmi6nnrZvpgInpobk6JywgaXRlbSk7CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcyhg5bey6YCJ5oupOiAke2l0ZW19YCk7CiAgICAgIHRoaXMuY2xvc2VGaWx0ZXJQb3B1cCgpOwogICAgfSwKCiAgICAvLyDngrnlh7vlpJbpg6jlhbPpl63lvLnnqpcKICAgIGhhbmRsZUNsaWNrT3V0c2lkZShldmVudCkgewogICAgICAvLyDlpITnkIbnrZvpgInlvLnnqpcKICAgICAgaWYgKHRoaXMuc2hvd0ZpbHRlclBvcHVwKSB7CiAgICAgICAgY29uc3QgZmlsdGVyQnV0dG9uID0gdGhpcy4kcmVmcy5maWx0ZXJCdXR0b247CiAgICAgICAgY29uc3QgcG9wdXAgPSBldmVudC50YXJnZXQuY2xvc2VzdCgnLmZpbHRlci1wb3B1cCcpOwoKICAgICAgICAvLyDlpoLmnpzngrnlh7vnmoTkuI3mmK/nrZvpgInmjInpkq7kuZ/kuI3mmK/lvLnnqpflhoXpg6jvvIzliJnlhbPpl63lvLnnqpcKICAgICAgICBpZiAoIWZpbHRlckJ1dHRvbj8uY29udGFpbnMoZXZlbnQudGFyZ2V0KSAmJiAhcG9wdXApIHsKICAgICAgICAgIHRoaXMuY2xvc2VGaWx0ZXJQb3B1cCgpOwogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5aSE55CG5pu05aSa5pON5L2c5by556qXCiAgICAgIGlmICh0aGlzLnNob3dNb3JlUG9wdXApIHsKICAgICAgICBjb25zdCBtb3JlQnV0dG9ucyA9IFsKICAgICAgICAgIHRoaXMuJHJlZnMubW9yZUJ1dHRvbjEsCiAgICAgICAgICB0aGlzLiRyZWZzLm1vcmVCdXR0b24yLAogICAgICAgICAgdGhpcy4kcmVmcy5tb3JlQnV0dG9uMwogICAgICAgIF07CiAgICAgICAgY29uc3QgbW9yZVBvcHVwID0gZXZlbnQudGFyZ2V0LmNsb3Nlc3QoJy5tb3JlLXBvcHVwJyk7CgogICAgICAgIC8vIOajgOafpeaYr+WQpueCueWHu+S6huS7u+S9leabtOWkmuaMiemSrgogICAgICAgIGNvbnN0IGNsaWNrZWRNb3JlQnV0dG9uID0gbW9yZUJ1dHRvbnMuc29tZShidXR0b24gPT4KICAgICAgICAgIGJ1dHRvbiAmJiBidXR0b24uY29udGFpbnMoZXZlbnQudGFyZ2V0KQogICAgICAgICk7CgogICAgICAgIC8vIOWmguaenOeCueWHu+eahOS4jeaYr+abtOWkmuaMiemSruS5n+S4jeaYr+W8ueeql+WGhemDqO+8jOWImeWFs+mXreW8ueeqlwogICAgICAgIGlmICghY2xpY2tlZE1vcmVCdXR0b24gJiYgIW1vcmVQb3B1cCkgewogICAgICAgICAgdGhpcy5jbG9zZU1vcmVQb3B1cCgpOwogICAgICAgIH0KICAgICAgfQoKICAgICAgLy8g5aSE55CG5YiG5Lqr5by556qXCiAgICAgIGlmICh0aGlzLnNob3dTaGFyZVBvcHVwKSB7CiAgICAgICAgY29uc3Qgc2hhcmVQb3B1cCA9IGV2ZW50LnRhcmdldC5jbG9zZXN0KCcuc2hhcmUtcG9wdXAnKTsKICAgICAgICAvLyDlpoLmnpzngrnlh7vnmoTkuI3mmK/lvLnnqpflhoXpg6jvvIzliJnlhbPpl63lvLnnqpcKICAgICAgICBpZiAoIXNoYXJlUG9wdXApIHsKICAgICAgICAgIHRoaXMuY2xvc2VTaGFyZVBvcHVwKCk7CiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDlpITnkIbljaHniYfmj5DphpLlvLnnqpcKICAgICAgaWYgKHRoaXMuc2hvd1JlbWluZGVyUG9wdXApIHsKICAgICAgICBjb25zdCByZW1pbmRlclBvcHVwID0gZXZlbnQudGFyZ2V0LmNsb3Nlc3QoJy5yZW1pbmRlci1wb3B1cCcpOwogICAgICAgIC8vIOWmguaenOeCueWHu+eahOS4jeaYr+W8ueeql+WGhemDqO+8jOWImeWFs+mXreW8ueeqlwogICAgICAgIGlmICghcmVtaW5kZXJQb3B1cCkgewogICAgICAgICAgdGhpcy5jbG9zZVJlbWluZGVyUG9wdXAoKTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOWkhOeQhuS4iuS8oENTVuW8ueeqlwogICAgICBpZiAodGhpcy5zaG93VXBsb2FkUG9wdXApIHsKICAgICAgICBjb25zdCB1cGxvYWRQb3B1cCA9IGV2ZW50LnRhcmdldC5jbG9zZXN0KCcudXBsb2FkLXBvcHVwJyk7CiAgICAgICAgLy8g5aaC5p6c54K55Ye755qE5LiN5piv5by556qX5YaF6YOo77yM5YiZ5YWz6Zet5by556qXCiAgICAgICAgaWYgKCF1cGxvYWRQb3B1cCkgewogICAgICAgICAgdGhpcy5jbG9zZVVwbG9hZFBvcHVwKCk7CiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDlpITnkIblm77ooajpgInmi6nlmajlvLnnqpcKICAgICAgaWYgKHRoaXMuc2hvd0NoYXJ0U2VsZWN0b3IpIHsKICAgICAgICBjb25zdCBjaGFydFNlbGVjdG9yID0gZXZlbnQudGFyZ2V0LmNsb3Nlc3QoJy5jaGFydC1zZWxlY3RvcicpOwogICAgICAgIC8vIOWmguaenOeCueWHu+eahOS4jeaYr+W8ueeql+WGhemDqO+8jOWImeWFs+mXreW8ueeqlwogICAgICAgIGlmICghY2hhcnRTZWxlY3RvcikgewogICAgICAgICAgdGhpcy5jbG9zZUNoYXJ0U2VsZWN0b3IoKTsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC8vIOWkhOeQhuiuvue9ruW8ueeqlwogICAgICBpZiAodGhpcy5zaG93U2V0dGluZ3NQb3B1cCkgewogICAgICAgIGNvbnN0IHNldHRpbmdzUG9wdXAgPSBldmVudC50YXJnZXQuY2xvc2VzdCgnLnNldHRpbmdzLXBvcHVwJyk7CiAgICAgICAgY29uc3Qgc2V0dGluZ3NCdXR0b25zID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvckFsbCgnLmFjdGlvbi1pY29uLnNldHRpbmdzJyk7CgogICAgICAgIC8vIOajgOafpeaYr+WQpueCueWHu+S6huS7u+S9leiuvue9ruaMiemSrgogICAgICAgIGNvbnN0IGNsaWNrZWRTZXR0aW5nc0J1dHRvbiA9IEFycmF5LmZyb20oc2V0dGluZ3NCdXR0b25zKS5zb21lKGJ1dHRvbiA9PgogICAgICAgICAgYnV0dG9uICYmIGJ1dHRvbi5jb250YWlucyhldmVudC50YXJnZXQpCiAgICAgICAgKTsKCiAgICAgICAgLy8g5aaC5p6c54K55Ye755qE5LiN5piv6K6+572u5oyJ6ZKu5Lmf5LiN5piv5by556qX5YaF6YOo77yM5YiZ5YWz6Zet5by556qXCiAgICAgICAgaWYgKCFjbGlja2VkU2V0dGluZ3NCdXR0b24gJiYgIXNldHRpbmdzUG9wdXApIHsKICAgICAgICAgIHRoaXMuY2xvc2VTZXR0aW5nc1BvcHVwKCk7CiAgICAgICAgfQogICAgICB9CiAgICB9LAoKICAgIC8vIOabtOWkmuaTjeS9nOebuOWFs+aWueazlQogICAgaGFuZGxlTW9yZUNsaWNrKGV2ZW50KSB7CiAgICAgIHRoaXMuc2hvd01vcmVQb3B1cCA9IHRydWU7CgogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy5zZXRNb3JlUG9wdXBQb3NpdGlvbihldmVudC50YXJnZXQpOwogICAgICB9KTsKICAgIH0sCgogICAgLy8g6K6+572u5pu05aSa5by556qX5L2N572uCiAgICBzZXRNb3JlUG9wdXBQb3NpdGlvbihidXR0b25FbGVtZW50KSB7CiAgICAgIGlmIChidXR0b25FbGVtZW50KSB7CiAgICAgICAgY29uc3QgcmVjdCA9IGJ1dHRvbkVsZW1lbnQuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCk7CiAgICAgICAgdGhpcy5tb3JlUG9wdXBTdHlsZSA9IHsKICAgICAgICAgIHBvc2l0aW9uOiAnZml4ZWQnLAogICAgICAgICAgdG9wOiByZWN0LmJvdHRvbSArIDUgKyAncHgnLAogICAgICAgICAgbGVmdDogcmVjdC5sZWZ0IC0gNjAgKyAncHgnLCAvLyDlkJHlt6blgY/np7vkuIDkupvvvIzorqnlvLnnqpflsYXkuK3lr7npvZDmjInpkq4KICAgICAgICAgIHpJbmRleDogMjAwMAogICAgICAgIH07CiAgICAgIH0KICAgIH0sCgogICAgY2xvc2VNb3JlUG9wdXAoKSB7CiAgICAgIHRoaXMuc2hvd01vcmVQb3B1cCA9IGZhbHNlOwogICAgfSwKCiAgICBoYW5kbGVDYXJkUmVtaW5kZXIoKSB7CiAgICAgIHRoaXMuc2hvd1JlbWluZGVyUG9wdXAgPSB0cnVlOwogICAgICB0aGlzLmNsb3NlTW9yZVBvcHVwKCk7CiAgICB9LAoKICAgIGhhbmRsZVNoYXJlQ2FyZCgpIHsKICAgICAgdGhpcy5zaG93U2hhcmVQb3B1cCA9IHRydWU7CiAgICAgIHRoaXMuY2xvc2VNb3JlUG9wdXAoKTsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfliIbkuqvlvLnnqpflt7LmiZPlvIAnKTsKICAgIH0sCgogICAgaGFuZGxlU2F2ZUNhcmQoKSB7CiAgICAgIGNvbnNvbGUubG9nKCfkv53lrZjljaHniYcnKTsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfkv53lrZjljaHniYflip/og73lvIDlj5HkuK0uLi4nKTsKICAgICAgdGhpcy5jbG9zZU1vcmVQb3B1cCgpOwogICAgfSwKCiAgICBoYW5kbGVVcGxvYWRDU1YoKSB7CiAgICAgIHRoaXMuc2hvd1VwbG9hZFBvcHVwID0gdHJ1ZTsKICAgICAgdGhpcy5jbG9zZU1vcmVQb3B1cCgpOwogICAgfSwKCiAgICBoYW5kbGVEb3dubG9hZFBORygpIHsKICAgICAgY29uc29sZS5sb2coJ+S4i+i9vVBORycpOwogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+S4i+i9vVBOR+WKn+iDveW8gOWPkeS4rS4uLicpOwogICAgICB0aGlzLmNsb3NlTW9yZVBvcHVwKCk7CiAgICB9LAoKICAgIC8vIOWbvuihqOaMiemSruWkhOeQhuaWueazlQogICAgaGFuZGxlUmVmcmVzaCgpIHsKICAgICAgY29uc29sZS5sb2coJ+WIt+aWsOaVsOaNricpOwogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+ato+WcqOWIt+aWsOaVsOaNri4uLicpOwogICAgICAvLyDph43mlrDliJ3lp4vljJblm77ooagKICAgICAgdGhpcy5pbml0Q2hhcnRzKCk7CiAgICB9LAoKICAgIGhhbmRsZURvd25sb2FkKCkgewogICAgICBjb25zb2xlLmxvZygn5LiL6L295pWw5o2uJyk7CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5LiL6L295Yqf6IO95byA5Y+R5LitLi4uJyk7CiAgICB9LAoKICAgIGhhbmRsZVNldHRpbmdzKGV2ZW50KSB7CiAgICAgIGNvbnNvbGUubG9nKCforr7nva4nKTsKICAgICAgdGhpcy5zaG93U2V0dGluZ3NQb3B1cCA9IHRydWU7CgogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy5zZXRTZXR0aW5nc1BvcHVwUG9zaXRpb24oZXZlbnQudGFyZ2V0KTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8vIOWIhuS6q+W8ueeql+ebuOWFs+aWueazlQogICAgY2xvc2VTaGFyZVBvcHVwKCkgewogICAgICB0aGlzLnNob3dTaGFyZVBvcHVwID0gZmFsc2U7CiAgICB9LAoKICAgIGNvcHlTaGFyZUxpbmsoKSB7CiAgICAgIC8vIOWkjeWItumTvuaOpeWIsOWJqui0tOadvwogICAgICBuYXZpZ2F0b3IuY2xpcGJvYXJkLndyaXRlVGV4dCh0aGlzLnNoYXJlTGluaykudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfpk77mjqXlt7LlpI3liLbliLDliarotLTmnb8nKTsKICAgICAgfSkuY2F0Y2goKCkgPT4gewogICAgICAgIC8vIOmZjee6p+aWueahiAogICAgICAgIGNvbnN0IHRleHRBcmVhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgndGV4dGFyZWEnKTsKICAgICAgICB0ZXh0QXJlYS52YWx1ZSA9IHRoaXMuc2hhcmVMaW5rOwogICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQodGV4dEFyZWEpOwogICAgICAgIHRleHRBcmVhLnNlbGVjdCgpOwogICAgICAgIGRvY3VtZW50LmV4ZWNDb21tYW5kKCdjb3B5Jyk7CiAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZCh0ZXh0QXJlYSk7CiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCfpk77mjqXlt7LlpI3liLbliLDliarotLTmnb8nKTsKICAgICAgfSk7CiAgICB9LAoKICAgIC8vIOWNoeeJh+aPkOmGkuW8ueeql+aWueazlQogICAgY2xvc2VSZW1pbmRlclBvcHVwKCkgewogICAgICB0aGlzLnNob3dSZW1pbmRlclBvcHVwID0gZmFsc2U7CiAgICAgIC8vIOmHjee9ruihqOWNlQogICAgICB0aGlzLnJlbWluZGVyRm9ybSA9IHsKICAgICAgICBjYXJkTmFtZTogJycsCiAgICAgICAgZW1haWw6ICcnLAogICAgICAgIGNoYW5nZVR5cGU6ICflkIzmr5Tlop7lh4/luYUnLAogICAgICAgIHRpbWVQZXJpb2Q6ICflpKnmlbAnLAogICAgICAgIHRocmVzaG9sZDogMCwKICAgICAgICBjb250ZW50Q2hhbmdlOiBmYWxzZSwKICAgICAgICBtZXRob2Q6ICdlbWFpbCcKICAgICAgfTsKICAgIH0sCgogICAgY29uZmlybVJlbWluZGVyKCkgewogICAgICAvLyDpqozor4HooajljZUKICAgICAgaWYgKCF0aGlzLnJlbWluZGVyRm9ybS5jYXJkTmFtZSkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup5o+Q6YaS5Y2h54mHJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIGlmICghdGhpcy5yZW1pbmRlckZvcm0uZW1haWwpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpemCrueuseWcsOWdgCcpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgLy8g6L+Z6YeM5Y+v5Lul5re75Yqg6YKu566x5qC85byP6aqM6K+BCiAgICAgIGNvbnN0IGVtYWlsUmVnZXggPSAvXlteXHNAXStAW15cc0BdK1wuW15cc0BdKyQvOwogICAgICBpZiAoIWVtYWlsUmVnZXgudGVzdCh0aGlzLnJlbWluZGVyRm9ybS5lbWFpbCkpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpeato+ehrueahOmCrueuseagvOW8jycpOwogICAgICAgIHJldHVybjsKICAgICAgfQoKICAgICAgY29uc29sZS5sb2coJ+iuvue9ruWNoeeJh+aPkOmGkjonLCB0aGlzLnJlbWluZGVyRm9ybSk7CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Y2h54mH5o+Q6YaS6K6+572u5oiQ5Yqf77yBJyk7CiAgICAgIHRoaXMuY2xvc2VSZW1pbmRlclBvcHVwKCk7CiAgICB9LAoKICAgIC8vIOS4iuS8oENTVuW8ueeql+aWueazlQogICAgY2xvc2VVcGxvYWRQb3B1cCgpIHsKICAgICAgdGhpcy5zaG93VXBsb2FkUG9wdXAgPSBmYWxzZTsKICAgICAgLy8g6YeN572u6KGo5Y2VCiAgICAgIHRoaXMudXBsb2FkRm9ybSA9IHsKICAgICAgICByZXBvcnROYW1lOiAnJywKICAgICAgICBkZXNjcmlwdGlvbjogJycsCiAgICAgICAgZmlsZTogbnVsbAogICAgICB9OwogICAgfSwKCiAgICBoYW5kbGVGaWxlU2VsZWN0KGV2ZW50KSB7CiAgICAgIGNvbnN0IGZpbGUgPSBldmVudC50YXJnZXQuZmlsZXNbMF07CiAgICAgIGlmIChmaWxlKSB7CiAgICAgICAgLy8g5qOA5p+l5paH5Lu257G75Z6LCiAgICAgICAgaWYgKCFmaWxlLm5hbWUudG9Mb3dlckNhc2UoKS5lbmRzV2l0aCgnLmNzdicpKSB7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+mAieaLqUNTVuagvOW8j+eahOaWh+S7ticpOwogICAgICAgICAgZXZlbnQudGFyZ2V0LnZhbHVlID0gJyc7CiAgICAgICAgICByZXR1cm47CiAgICAgICAgfQogICAgICAgIHRoaXMudXBsb2FkRm9ybS5maWxlID0gZmlsZTsKICAgICAgfQogICAgfSwKCiAgICBjb25maXJtVXBsb2FkKCkgewogICAgICAvLyDpqozor4HooajljZUKICAgICAgaWYgKCF0aGlzLnVwbG9hZEZvcm0ucmVwb3J0TmFtZS50cmltKCkpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpeaKpeWRiuWQjeensCcpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICBpZiAoIXRoaXMudXBsb2FkRm9ybS5maWxlKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfor7fpgInmi6nopoHkuIrkvKDnmoRDU1bmlofku7YnKTsKICAgICAgICByZXR1cm47CiAgICAgIH0KCiAgICAgIGNvbnNvbGUubG9nKCfkuIrkvKBDU1Y6JywgdGhpcy51cGxvYWRGb3JtKTsKICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCdDU1bmlofku7bkuIrkvKDmiJDlip/vvIEnKTsKICAgICAgdGhpcy5jbG9zZVVwbG9hZFBvcHVwKCk7CiAgICB9LAoKICAgIC8vIOWbvuihqOmAieaLqeWZqOebuOWFs+aWueazlQogICAgb3BlbkNoYXJ0U2VsZWN0b3IoKSB7CiAgICAgIHRoaXMuc2hvd0NoYXJ0U2VsZWN0b3IgPSB0cnVlOwogICAgfSwKCiAgICBjbG9zZUNoYXJ0U2VsZWN0b3IoKSB7CiAgICAgIHRoaXMuc2hvd0NoYXJ0U2VsZWN0b3IgPSBmYWxzZTsKICAgIH0sCgogICAgc2VsZWN0Q2hhcnRUeXBlKGNoYXJ0VHlwZSkgewogICAgICB0aGlzLnNlbGVjdGVkQ2hhcnRUeXBlID0gY2hhcnRUeXBlOwogICAgICBjb25zb2xlLmxvZygn6YCJ5oup5Zu+6KGo57G75Z6LOicsIGNoYXJ0VHlwZSk7CiAgICAgIHRoaXMuY2xvc2VDaGFydFNlbGVjdG9yKCk7CiAgICB9LAoKICAgIC8vIOiuvue9ruW8ueeql+ebuOWFs+aWueazlQogICAgc2V0U2V0dGluZ3NQb3B1cFBvc2l0aW9uKGJ1dHRvbkVsZW1lbnQpIHsKICAgICAgaWYgKGJ1dHRvbkVsZW1lbnQpIHsKICAgICAgICBjb25zdCByZWN0ID0gYnV0dG9uRWxlbWVudC5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKTsKICAgICAgICB0aGlzLnNldHRpbmdzUG9wdXBTdHlsZSA9IHsKICAgICAgICAgIHBvc2l0aW9uOiAnZml4ZWQnLAogICAgICAgICAgdG9wOiByZWN0LmJvdHRvbSArIDUgKyAncHgnLAogICAgICAgICAgbGVmdDogcmVjdC5sZWZ0IC0gMTAwICsgJ3B4JywKICAgICAgICAgIHpJbmRleDogMjAwMAogICAgICAgIH07CiAgICAgIH0KICAgIH0sCgogICAgY2xvc2VTZXR0aW5nc1BvcHVwKCkgewogICAgICB0aGlzLnNob3dTZXR0aW5nc1BvcHVwID0gZmFsc2U7CiAgICB9LAoKICAgIHNlbGVjdENoYXJ0SWNvbihjaGFydFR5cGUpIHsKICAgICAgY29uc29sZS5sb2coJ+mAieaLqeWbvuihqOexu+WeizonLCBjaGFydFR5cGUpOwogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoYOW3sumAieaLqeWbvuihqOexu+WeizogJHtjaGFydFR5cGV9YCk7CiAgICAgIHRoaXMuY2xvc2VTZXR0aW5nc1BvcHVwKCk7CiAgICB9CiAgfQp9Owo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAisBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/datasearch", "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <!-- 顶部搜索栏 -->\n    <div class=\"search-container\">\n      <div class=\"search-form\">\n        <div class=\"search-input-wrapper\">\n          <input\n            type=\"text\"\n            v-model=\"searchForm.keyword\"\n            class=\"search-input\"\n            placeholder=\"搜索 门店营业额 前十 门店 营业额\"\n          />\n        </div>\n        <div class=\"search-buttons\">\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleClose\">\n            <i class=\"close-icon\"></i>\n          </button>\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleSearch\">\n            <i class=\"search-icon\"></i>\n          </button>\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleMinus\">\n            <i class=\"minus-icon\"></i>\n          </button>\n          <button type=\"button\" class=\"btn-icon\" @click=\"handleFilter\" ref=\"filterButton\">\n            <i class=\"filter-icon\"></i>\n          </button>\n        </div>\n\n        <!-- 筛选弹窗 -->\n        <div v-if=\"showFilterPopup\" class=\"filter-popup\" :style=\"filterPopupStyle\">\n          <div class=\"popup-header\">\n            <span>数据</span>\n            <button class=\"popup-close\" @click=\"closeFilterPopup\">×</button>\n          </div>\n          <div class=\"popup-search\">\n            <input type=\"text\" class=\"search-input\" placeholder=\"搜索\" v-model=\"filterSearchQuery\">\n            <i class=\"search-icon\"></i>\n          </div>\n          <div class=\"popup-tabs\">\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '维度' }\" @click=\"activeTab = '维度'\">维度</div>\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '时间维度' }\" @click=\"activeTab = '时间维度'\">时间维度</div>\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '指标' }\" @click=\"activeTab = '指标'\">指标</div>\n            <div class=\"tab-item\" :class=\"{ active: activeTab === '分析' }\" @click=\"activeTab = '分析'\">分析</div>\n          </div>\n          <div class=\"popup-content\">\n            <div v-if=\"activeTab === '维度'\" class=\"tab-content\">\n              <div class=\"filter-item\" @click=\"selectFilter('门店')\">\n                <span>门店</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('品牌')\">\n                <span>品牌</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('等')\">\n                <span>等</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('综合分析')\">\n                <span>综合分析</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('门店营业额')\">\n                <span>门店营业额</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n              <div class=\"filter-item\" @click=\"selectFilter('数据分析')\">\n                <span>数据分析</span>\n                <i class=\"arrow-icon\">^</i>\n              </div>\n            </div>\n            <div v-if=\"activeTab === '时间维度'\" class=\"tab-content\">\n              <div class=\"filter-item\">\n                <span>日期</span>\n                <i class=\"arrow-icon down\">v</i>\n              </div>\n              <div class=\"time-units-row\">\n                <span class=\"time-unit\" @click=\"selectFilter('日')\">日</span>\n                <span class=\"time-unit\" @click=\"selectFilter('周')\">周</span>\n                <span class=\"time-unit\" @click=\"selectFilter('月')\">月</span>\n                <span class=\"time-unit\" @click=\"selectFilter('季')\">季</span>\n                <span class=\"time-unit\" @click=\"selectFilter('年')\">年</span>\n              </div>\n              <div class=\"time-item\">当日</div>\n              <div class=\"time-item\">数天</div>\n              <div class=\"time-item\">数十天</div>\n              <div class=\"time-item\">数月</div>\n              <div class=\"time-item\">2月1日至16日</div>\n              <div class=\"time-item\">2月1日至今</div>\n            </div>\n            <div v-if=\"activeTab === '指标'\" class=\"tab-content\">\n              <div class=\"filter-item\" @click=\"selectFilter('出店')\">出店</div>\n              <div class=\"filter-item\" @click=\"selectFilter('进店顾客')\">进店顾客</div>\n              <div class=\"filter-item\" @click=\"selectFilter('客单')\">客单</div>\n              <div class=\"filter-item\" @click=\"selectFilter('分析')\">分析</div>\n              <div class=\"filter-item\" @click=\"selectFilter('利润')\">利润</div>\n              <div class=\"filter-item\" @click=\"selectFilter('销售额')\">销售额</div>\n              <div class=\"filter-item\" @click=\"selectFilter('进货数量')\">进货数量</div>\n              <div class=\"filter-item\" @click=\"selectFilter('退货数量')\">退货数量</div>\n              <div class=\"filter-item\" @click=\"selectFilter('总价值')\">总价值</div>\n              <div class=\"filter-item\" @click=\"selectFilter('公司利润率')\">公司利润率</div>\n              <div class=\"filter-item\" @click=\"selectFilter('客户数量')\">客户数量</div>\n              <div class=\"filter-item\" @click=\"selectFilter('今日利润')\">今日利润</div>\n              <div class=\"filter-item\" @click=\"selectFilter('全店成本率')\">全店成本率</div>\n            </div>\n            <div v-if=\"activeTab === '分析'\" class=\"tab-content\">\n              <div class=\"filter-item\" @click=\"selectFilter('出店')\">出店</div>\n              <div class=\"filter-item\" @click=\"selectFilter('增长')\">增长</div>\n              <div class=\"filter-item\" @click=\"selectFilter('开店')\">开店</div>\n              <div class=\"filter-item\" @click=\"selectFilter('同比')\">同比</div>\n              <div class=\"filter-item\" @click=\"selectFilter('成交率')\">成交率</div>\n              <div class=\"filter-item\" @click=\"selectFilter('分析')\">分析</div>\n              <div class=\"filter-item\" @click=\"selectFilter('同比')\">同比</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 顶部：门店营业额前十的 + 智能助手 -->\n    <div class=\"top-section\">\n      <div class=\"chart-card main-chart\">\n        <div class=\"chart-header\">\n          <div class=\"chart-title\">\n            <i class=\"chart-icon\"></i>\n            <span>门店营业额前十的</span>\n            <i class=\"help-icon\">?</i>\n          </div>\n          <div class=\"chart-meta\">\n            <span class=\"chart-date\">2024-01-01 至 12-31</span>\n            <span class=\"chart-type\">月报</span>\n            <span class=\"chart-source\">按营业额排序</span>\n          </div>\n          <div class=\"chart-actions\">\n            <i class=\"action-icon refresh\" @click=\"handleRefresh\"></i>\n            <i class=\"action-icon download\" @click=\"handleDownload\"></i>\n            <i class=\"action-icon more\" ref=\"moreButton1\" @click=\"handleMoreClick($event)\"></i>\n            <i class=\"action-icon settings\" @click=\"handleSettings\"></i>\n            <span class=\"chart-status\">数据加载中</span>\n            <i class=\"action-icon close\" @click=\"handleClose\"></i>\n          </div>\n        </div>\n        <div class=\"chart-content\">\n          <div class=\"chart-legend\">\n            <div class=\"legend-item\">\n              <span class=\"legend-color blue\"></span>\n              <span>营业额/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color yellow\"></span>\n              <span>利润/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color line\"></span>\n              <span>营业额同比增长率</span>\n            </div>\n          </div>\n          <div class=\"chart-wrapper\">\n            <div ref=\"storeRevenueChart\" class=\"chart\" style=\"height: 300px;\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能助手面板 -->\n      <div class=\"assistant-panel\">\n        <div class=\"panel-header\">\n          <span>智能助手</span>\n          <div class=\"header-actions\">\n            <button class=\"send-btn\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"white\"/>\n              </svg>\n            </button>\n            <i class=\"panel-close\">×</i>\n          </div>\n        </div>\n        <div class=\"panel-content\">\n          <div class=\"chat-messages\">\n            <div class=\"message-item assistant-message\">\n              <div class=\"message-avatar\">\n                <div class=\"avatar-circle\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.5 10.84L11.91 12.25L15.83 8.33L17.5 10H19V9H21ZM1 9H3V7H1V9ZM13 12L10.5 9.5L9.09 10.91L13 14.82L13 12ZM5 13L6.5 14.5L3.91 17.09L2.5 15.67L5 13ZM12 15C10.9 15 10 15.9 10 17C10 18.1 10.9 19 12 19C13.1 19 14 18.1 14 17C14 15.9 13.1 15 12 15Z\" fill=\"#1890ff\"/>\n                  </svg>\n                </div>\n              </div>\n              <div class=\"message-content\">\n                <div class=\"message-text\">根据当前数据表现？</div>\n                <div class=\"message-time\">刚刚</div>\n              </div>\n            </div>\n            <div class=\"suggestion-item\">\n              <div class=\"suggestion-icon\">💡</div>\n              <div class=\"suggestion-text\">深圳门店营业额最高，有什么成功经验可以分享？</div>\n            </div>\n            <div class=\"suggestion-item\">\n              <div class=\"suggestion-icon\">📊</div>\n              <div class=\"suggestion-text\">如何提升其他门店的营业额？</div>\n            </div>\n            <div class=\"suggestion-item\">\n              <div class=\"suggestion-icon\">🎯</div>\n              <div class=\"suggestion-text\">引用数据分析</div>\n            </div>\n          </div>\n          <div class=\"input-area\">\n            <div class=\"input-wrapper\">\n              <input type=\"text\" placeholder=\"请输入您的问题...\" class=\"chat-input\">\n              <button class=\"input-send-btn\">\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"#1890ff\"/>\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 中间：营业额同比 单独一行 -->\n    <div class=\"middle-section\">\n      <div class=\"value-card\">\n        <div class=\"value-header\">\n          <div class=\"value-title\">\n            <i class=\"chart-icon\"></i>\n            <span>营业额同比</span>\n            <i class=\"help-icon\">?</i>\n          </div>\n          <div class=\"value-meta\">\n            <span class=\"value-date\">2024-01-01 至 12-31</span>\n            <span class=\"value-type\">月报</span>\n          </div>\n          <div class=\"value-actions\">\n            <i class=\"action-icon refresh\" @click=\"handleRefresh\"></i>\n            <i class=\"action-icon more\" ref=\"moreButton2\" @click=\"handleMoreClick($event)\"></i>\n            <i class=\"action-icon settings\" @click=\"handleSettings\"></i>\n          </div>\n        </div>\n        <div class=\"value-content\">\n          <div class=\"value-main\">\n            <span class=\"value-label\">营业额(总) / 元</span>\n            <div class=\"value-number\">165.32<span class=\"value-unit\">亿</span></div>\n            <div class=\"value-change\">\n              <span class=\"change-text\">同比上期</span>\n              <span class=\"change-value positive\">+4.73%(+7.43亿)</span>\n              <i class=\"change-arrow up\"></i>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 底部：品牌门店营业额前十的 + 智能助手 -->\n    <div class=\"bottom-section\">\n      <div class=\"chart-card main-chart\">\n        <div class=\"chart-header\">\n          <div class=\"chart-title\">\n            <i class=\"chart-icon\"></i>\n            <span>品牌门店营业额前十的</span>\n            <i class=\"help-icon\">?</i>\n          </div>\n          <div class=\"chart-meta\">\n            <span class=\"chart-date\">2024-01-01 至 12-31</span>\n            <span class=\"chart-type\">月报</span>\n            <span class=\"chart-source\">按营业额排序</span>\n          </div>\n          <div class=\"chart-actions\">\n            <i class=\"action-icon refresh\" @click=\"handleRefresh\"></i>\n            <i class=\"action-icon download\" @click=\"handleDownload\"></i>\n            <i class=\"action-icon more\" ref=\"moreButton3\" @click=\"handleMoreClick($event)\"></i>\n            <i class=\"action-icon settings\" @click=\"handleSettings\"></i>\n            <span class=\"chart-status\">数据加载中</span>\n            <i class=\"action-icon close\" @click=\"handleClose\"></i>\n          </div>\n        </div>\n        <div class=\"chart-content\">\n          <div class=\"chart-legend\">\n            <div class=\"legend-item\">\n              <span class=\"legend-color blue\"></span>\n              <span>营业额/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color yellow\"></span>\n              <span>利润/万元</span>\n            </div>\n            <div class=\"legend-item\">\n              <span class=\"legend-color line\"></span>\n              <span>营业额同比增长率</span>\n            </div>\n          </div>\n          <div class=\"chart-wrapper\">\n            <div ref=\"cloudRevenueChart\" class=\"chart\" style=\"height: 300px;\"></div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 智能助手面板 -->\n      <div class=\"assistant-panel\">\n        <div class=\"panel-header\">\n          <span>智能助手</span>\n          <div class=\"header-actions\">\n            <button class=\"send-btn\">\n              <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"white\"/>\n              </svg>\n            </button>\n            <i class=\"panel-close\">×</i>\n          </div>\n        </div>\n        <div class=\"panel-content\">\n          <div class=\"chat-messages\">\n            <div class=\"message-item assistant-message\">\n              <div class=\"message-avatar\">\n                <div class=\"avatar-circle\">\n                  <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                    <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.5 10.84L11.91 12.25L15.83 8.33L17.5 10H19V9H21ZM1 9H3V7H1V9ZM13 12L10.5 9.5L9.09 10.91L13 14.82L13 12ZM5 13L6.5 14.5L3.91 17.09L2.5 15.67L5 13ZM12 15C10.9 15 10 15.9 10 17C10 18.1 10.9 19 12 19C13.1 19 14 18.1 14 17C14 15.9 13.1 15 12 15Z\" fill=\"#1890ff\"/>\n                  </svg>\n                </div>\n              </div>\n              <div class=\"message-content\">\n                <div class=\"message-text\">根据当前数据表现？</div>\n                <div class=\"message-time\">刚刚</div>\n              </div>\n            </div>\n            <div class=\"suggestion-item\">\n              <div class=\"suggestion-icon\">💡</div>\n              <div class=\"suggestion-text\">深圳门店营业额最高，有什么成功经验可以分享？</div>\n            </div>\n            <div class=\"suggestion-item\">\n              <div class=\"suggestion-icon\">📊</div>\n              <div class=\"suggestion-text\">如何提升其他门店的营业额？</div>\n            </div>\n            <div class=\"suggestion-item\">\n              <div class=\"suggestion-icon\">🎯</div>\n              <div class=\"suggestion-text\">引用数据分析</div>\n            </div>\n          </div>\n          <div class=\"input-area\">\n            <div class=\"input-wrapper\">\n              <input type=\"text\" placeholder=\"请输入您的问题...\" class=\"chat-input\">\n              <button class=\"input-send-btn\">\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"#1890ff\"/>\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 更多操作弹窗 -->\n    <div v-if=\"showMorePopup\" class=\"more-popup\" :style=\"morePopupStyle\" @click.stop>\n      <div class=\"more-popup-content\">\n        <div class=\"more-action-item\" @click.stop=\"handleCardReminder\">卡片提醒</div>\n        <div class=\"more-action-item\" @click.stop=\"handleShareCard\">分享卡片</div>\n        <div class=\"more-action-item\" @click=\"handleSaveCard\">保存卡片</div>\n        <div class=\"more-action-item\" @click.stop=\"handleUploadCSV\">上传CSV</div>\n        <div class=\"more-action-item\" @click=\"handleDownloadPNG\">下载PNG</div>\n      </div>\n    </div>\n\n    <!-- 分享卡片弹窗 -->\n    <div v-if=\"showSharePopup\" class=\"share-popup-overlay\" @click=\"closeSharePopup\">\n      <div class=\"share-popup\" @click.stop>\n        <div class=\"share-popup-header\">\n          <span class=\"share-popup-title\">分享链接</span>\n          <button class=\"share-popup-close\" @click=\"closeSharePopup\">×</button>\n        </div>\n        <div class=\"share-popup-content\">\n          <div class=\"share-description\">\n            分享分析结果，让更多的人看到你的洞察\n          </div>\n          <div class=\"share-option\">\n            <div class=\"share-option-label\">\n              <span>代码嵌入功能</span>\n            </div>\n            <div class=\"share-toggle\">\n              <input type=\"checkbox\" id=\"embedToggle\" v-model=\"embedEnabled\" class=\"toggle-input\">\n              <label for=\"embedToggle\" class=\"toggle-label\"></label>\n            </div>\n          </div>\n          <div class=\"share-link-section\">\n            <input\n              type=\"text\"\n              class=\"share-link-input\"\n              :value=\"shareLink\"\n              readonly\n              placeholder=\"https://dwz.cn/jzwMdMh\"\n            >\n            <button class=\"copy-link-btn\" @click=\"copyShareLink\">复制链接</button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 卡片提醒弹窗 -->\n    <div v-if=\"showReminderPopup\" class=\"reminder-popup-overlay\" @click=\"closeReminderPopup\">\n      <div class=\"reminder-popup\" @click.stop>\n        <div class=\"reminder-popup-header\">\n          <span class=\"reminder-popup-title\">卡片提醒设置</span>\n          <button class=\"reminder-popup-close\" @click=\"closeReminderPopup\">×</button>\n        </div>\n        <div class=\"reminder-popup-content\">\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">提醒卡片</label>\n            <select class=\"reminder-select\" v-model=\"reminderForm.cardName\">\n              <option value=\"\">请选择卡片</option>\n              <option value=\"门店营业额前十\">门店营业额前十</option>\n              <option value=\"云营业额前十\">云营业额前十</option>\n            </select>\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">提醒邮箱地址</label>\n            <input\n              type=\"email\"\n              class=\"reminder-input\"\n              v-model=\"reminderForm.email\"\n              placeholder=\"请输入邮箱地址\"\n            >\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">数据变化</label>\n            <div class=\"reminder-change-section\">\n              <select class=\"reminder-select-small\" v-model=\"reminderForm.changeType\">\n                <option value=\"同比增减幅\">同比增减幅/元</option>\n                <option value=\"环比增减幅\">环比增减幅/元</option>\n              </select>\n              <select class=\"reminder-select-small\" v-model=\"reminderForm.timePeriod\">\n                <option value=\"天数\">天数(天)</option>\n                <option value=\"周数\">周数(周)</option>\n                <option value=\"月数\">月数(月)</option>\n              </select>\n            </div>\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <div class=\"reminder-threshold-section\">\n              <input\n                type=\"number\"\n                class=\"reminder-number-input\"\n                v-model=\"reminderForm.threshold\"\n                placeholder=\"0\"\n              >\n              <span class=\"reminder-unit\">元</span>\n              <div class=\"reminder-checkbox-section\">\n                <input\n                  type=\"checkbox\"\n                  id=\"contentChange\"\n                  v-model=\"reminderForm.contentChange\"\n                  class=\"reminder-checkbox\"\n                >\n                <label for=\"contentChange\" class=\"reminder-checkbox-label\">内容变化提醒</label>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"reminder-description\">\n            当选择指标比上月数据变化超过设定阈值时，发送邮件提醒\n          </div>\n\n          <div class=\"reminder-form-item\">\n            <label class=\"reminder-label\">提醒方式</label>\n            <div class=\"reminder-method-section\">\n              <div class=\"reminder-radio-item\">\n                <input\n                  type=\"radio\"\n                  id=\"emailMethod\"\n                  value=\"email\"\n                  v-model=\"reminderForm.method\"\n                  class=\"reminder-radio\"\n                >\n                <label for=\"emailMethod\" class=\"reminder-radio-label\">邮件提醒</label>\n              </div>\n              <div class=\"reminder-radio-item\">\n                <input\n                  type=\"radio\"\n                  id=\"smsMethod\"\n                  value=\"sms\"\n                  v-model=\"reminderForm.method\"\n                  class=\"reminder-radio\"\n                >\n                <label for=\"smsMethod\" class=\"reminder-radio-label\">短信提醒</label>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"reminder-popup-footer\">\n          <button class=\"reminder-cancel-btn\" @click=\"closeReminderPopup\">取消</button>\n          <button class=\"reminder-confirm-btn\" @click=\"confirmReminder\">确定，设置提醒人</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 上传CSV弹窗 -->\n    <div v-if=\"showUploadPopup\" class=\"upload-popup-overlay\" @click=\"closeUploadPopup\">\n      <div class=\"upload-popup\" @click.stop>\n        <div class=\"upload-popup-header\">\n          <span class=\"upload-popup-title\">加入报告</span>\n          <button class=\"upload-popup-close\" @click=\"closeUploadPopup\">×</button>\n        </div>\n        <div class=\"upload-popup-content\">\n          <div class=\"upload-form-item\">\n            <label class=\"upload-label\">报告名称</label>\n            <input\n              type=\"text\"\n              class=\"upload-input\"\n              v-model=\"uploadForm.reportName\"\n              placeholder=\"请输入报告名称\"\n            >\n          </div>\n\n          <div class=\"upload-form-item\">\n            <label class=\"upload-label\">描述信息</label>\n            <textarea\n              class=\"upload-textarea\"\n              v-model=\"uploadForm.description\"\n              placeholder=\"请输入描述信息\"\n              rows=\"3\"\n            ></textarea>\n          </div>\n\n          <div class=\"upload-form-item\">\n            <label class=\"upload-label\">上传文件</label>\n            <div class=\"upload-file-section\">\n              <input\n                type=\"file\"\n                accept=\".csv\"\n                @change=\"handleFileSelect\"\n                class=\"upload-file-input\"\n                id=\"csvFileInput\"\n              >\n              <label for=\"csvFileInput\" class=\"upload-file-button\">\n                选择文件\n              </label>\n              <span class=\"upload-file-name\" v-if=\"uploadForm.file\">\n                {{ uploadForm.file.name }}\n              </span>\n              <span class=\"upload-file-placeholder\" v-else>\n                请选择CSV文件\n              </span>\n            </div>\n          </div>\n\n          <div class=\"upload-tips\">\n            <div class=\"upload-tips-title\">上传说明：</div>\n            <div class=\"upload-tips-content\">\n              • 支持CSV格式文件<br>\n              • 文件大小不超过10MB<br>\n              • 请确保数据格式正确\n            </div>\n          </div>\n        </div>\n\n        <div class=\"upload-popup-footer\">\n          <button class=\"upload-cancel-btn\" @click=\"closeUploadPopup\">取消</button>\n          <button class=\"upload-confirm-btn\" @click=\"confirmUpload\">确定</button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 设置弹窗 -->\n    <div v-if=\"showSettingsPopup\" class=\"settings-popup\" :style=\"settingsPopupStyle\" @click.stop>\n      <div class=\"settings-popup-content\">\n        <div class=\"chart-types-grid\">\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('bar')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <rect x=\"3\" y=\"12\" width=\"4\" height=\"9\" fill=\"#1890ff\"/>\n                <rect x=\"10\" y=\"8\" width=\"4\" height=\"13\" fill=\"#1890ff\"/>\n                <rect x=\"17\" y=\"4\" width=\"4\" height=\"17\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">柱状图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('line')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M3 17L9 11L13 15L21 7\" stroke=\"#1890ff\" stroke-width=\"2\" fill=\"none\"/>\n                <circle cx=\"3\" cy=\"17\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"9\" cy=\"11\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"13\" cy=\"15\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"21\" cy=\"7\" r=\"2\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">折线图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('pie')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M12 2V12L20.5 7.5C19.5 4.5 16 2 12 2Z\" fill=\"#1890ff\"/>\n                <path d=\"M12 12L20.5 16.5C19.5 19.5 16 22 12 22C7 22 3 18 3 12C3 7 7 3 12 3V12Z\" fill=\"#52c41a\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">饼图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('area')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M3 17L9 11L13 15L21 7V21H3V17Z\" fill=\"#1890ff\" opacity=\"0.3\"/>\n                <path d=\"M3 17L9 11L13 15L21 7\" stroke=\"#1890ff\" stroke-width=\"2\" fill=\"none\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">面积图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('scatter')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <circle cx=\"5\" cy=\"18\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"9\" cy=\"12\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"13\" cy=\"16\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"17\" cy=\"8\" r=\"2\" fill=\"#1890ff\"/>\n                <circle cx=\"21\" cy=\"14\" r=\"2\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">散点图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('radar')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <polygon points=\"12,2 20,8 20,16 12,22 4,16 4,8\" stroke=\"#1890ff\" stroke-width=\"1\" fill=\"none\"/>\n                <polygon points=\"12,6 16,9 16,15 12,18 8,15 8,9\" stroke=\"#1890ff\" stroke-width=\"1\" fill=\"#1890ff\" opacity=\"0.3\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">雷达图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('gauge')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z\" stroke=\"#1890ff\" stroke-width=\"2\" fill=\"none\"/>\n                <path d=\"M12 12L16 8\" stroke=\"#1890ff\" stroke-width=\"2\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"2\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">仪表盘</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('funnel')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M6 4H18L16 8H8L6 4Z\" fill=\"#1890ff\"/>\n                <path d=\"M8 8H16L14 12H10L8 8Z\" fill=\"#52c41a\"/>\n                <path d=\"M10 12H14L13 16H11L10 12Z\" fill=\"#faad14\"/>\n                <path d=\"M11 16H13V20H11V16Z\" fill=\"#f5222d\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">漏斗图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('heatmap')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <rect x=\"3\" y=\"3\" width=\"4\" height=\"4\" fill=\"#1890ff\"/>\n                <rect x=\"8\" y=\"3\" width=\"4\" height=\"4\" fill=\"#52c41a\"/>\n                <rect x=\"13\" y=\"3\" width=\"4\" height=\"4\" fill=\"#faad14\"/>\n                <rect x=\"18\" y=\"3\" width=\"3\" height=\"4\" fill=\"#f5222d\"/>\n                <rect x=\"3\" y=\"8\" width=\"4\" height=\"4\" fill=\"#52c41a\"/>\n                <rect x=\"8\" y=\"8\" width=\"4\" height=\"4\" fill=\"#faad14\"/>\n                <rect x=\"13\" y=\"8\" width=\"4\" height=\"4\" fill=\"#f5222d\"/>\n                <rect x=\"18\" y=\"8\" width=\"3\" height=\"4\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">热力图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('treemap')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <rect x=\"2\" y=\"2\" width=\"10\" height=\"8\" fill=\"#1890ff\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"13\" y=\"2\" width=\"9\" height=\"5\" fill=\"#52c41a\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"13\" y=\"8\" width=\"9\" height=\"3\" fill=\"#faad14\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"2\" y=\"11\" width=\"6\" height=\"11\" fill=\"#f5222d\" stroke=\"#fff\" stroke-width=\"1\"/>\n                <rect x=\"9\" y=\"11\" width=\"13\" height=\"11\" fill=\"#722ed1\" stroke=\"#fff\" stroke-width=\"1\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">矩形树图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('sunburst')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"none\" stroke=\"#1890ff\" stroke-width=\"2\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"6\" fill=\"none\" stroke=\"#52c41a\" stroke-width=\"2\"/>\n                <circle cx=\"12\" cy=\"12\" r=\"3\" fill=\"#faad14\"/>\n                <path d=\"M12 2L14 6L12 6L10 6L12 2Z\" fill=\"#1890ff\"/>\n                <path d=\"M22 12L18 14L18 12L18 10L22 12Z\" fill=\"#1890ff\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">旭日图</span>\n          </div>\n          <div class=\"chart-type-item\" @click=\"selectChartIcon('sankey')\">\n            <div class=\"chart-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path d=\"M2 6C2 6 8 6 12 10C16 14 22 14 22 14\" stroke=\"#1890ff\" stroke-width=\"3\" fill=\"none\"/>\n                <path d=\"M2 12C2 12 8 12 12 12C16 12 22 12 22 12\" stroke=\"#52c41a\" stroke-width=\"3\" fill=\"none\"/>\n                <path d=\"M2 18C2 18 8 18 12 14C16 10 22 10 22 10\" stroke=\"#faad14\" stroke-width=\"3\" fill=\"none\"/>\n              </svg>\n            </div>\n            <span class=\"chart-label\">桑基图</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts';\n\nexport default {\n  name: \"DataSearch\",\n  data() {\n    return {\n      // 搜索表单数据\n      searchForm: {\n        keyword: '门店 营业额 前十 门店 营业额',\n        dataType: 'store',\n        store: 'all',\n        time: '2024'\n      },\n      // 筛选弹窗相关\n      showFilterPopup: false,\n      filterPopupStyle: {},\n      filterSearchQuery: '',\n      activeTab: '维度',\n      // 更多操作弹窗\n      showMorePopup: false,\n      morePopupStyle: {},\n      // 分享弹窗相关\n      showSharePopup: false,\n      embedEnabled: true,\n      shareLink: 'https://dwz.cn/jzwMdMh',\n\n      // 卡片提醒弹窗\n      showReminderPopup: false,\n      reminderForm: {\n        cardName: '',\n        email: '',\n        changeType: '同比增减幅',\n        timePeriod: '天数',\n        threshold: 0,\n        contentChange: false,\n        method: 'email'\n      },\n      // 上传CSV弹窗\n      showUploadPopup: false,\n      uploadForm: {\n        reportName: '',\n        description: '',\n        file: null\n      },\n      // 图表选择弹窗\n      showChartSelector: false,\n      selectedChartType: 'bar',\n      // 设置弹窗\n      showSettingsPopup: false,\n      settingsPopupStyle: {},\n      // 图表实例\n      storeRevenueChart: null,\n      cloudRevenueChart: null,\n      // 刷新间隔\n      refreshInterval: '0',\n      // 门店营业额数据\n      storeRevenueData: {\n        categories: ['深圳', '广州', '北京', '上海', '杭州', '南京', '成都', '重庆', '武汉', '西安', '天津'],\n        revenue: [21340, 16200, 14100, 8100, 7610, 6200, 5310, 4860, 4340, 3460, 3140],\n        profit: [22410, 18940, 14200, 12400, 7600, 6420, 5400, 4740, 4360, 3740, 3140],\n        growthRate: [11.39, 9.04, 8.31, 7.60, 5.37, 5.04, 4.74, 4.34, 4.17, 3.86, 3.70]\n      },\n      // 云营业额数据\n      cloudRevenueData: {\n        categories: ['华南大区_深圳', '华南大区_广州', '华东大区_上海', '华东大区_杭州', '华北大区_北京', '华中大区_武汉', '西南大区_成都', '西南大区_重庆', '西北大区_西安', '华北大区_天津', '华东大区_南京'],\n        revenue: [6000000, 5800000, 4100000, 4100000, 3400000, 2600000, 2400000, 2100000, 2000000, 1900000, 1800000],\n        profit: [5800000, 5600000, 4100000, 4100000, 3200000, 2400000, 2200000, 2000000, 1900000, 1800000, 1700000],\n        growthRate: [4.70, -0.2, -6.3, -6.3, 1.9, 2.6, 2.7, 2.1, 2.0, 1.9, 1.8]\n      }\n    };\n  },\n  mounted() {\n    this.initCharts();\n    // 添加点击外部关闭弹窗的事件监听\n    document.addEventListener('click', this.handleClickOutside);\n  },\n  beforeDestroy() {\n    if (this.storeRevenueChart) {\n      this.storeRevenueChart.dispose();\n    }\n    if (this.cloudRevenueChart) {\n      this.cloudRevenueChart.dispose();\n    }\n    // 移除事件监听\n    document.removeEventListener('click', this.handleClickOutside);\n  },\n  methods: {\n    /** 初始化图表 */\n    initCharts() {\n      this.$nextTick(() => {\n        this.initStoreRevenueChart();\n        this.initCloudRevenueChart();\n      });\n    },\n\n    /** 初始化门店营业额图表 */\n    initStoreRevenueChart() {\n      this.storeRevenueChart = echarts.init(this.$refs.storeRevenueChart);\n\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          }\n        },\n        legend: {\n          show: false\n        },\n        xAxis: [\n          {\n            type: 'category',\n            data: this.storeRevenueData.categories,\n            axisPointer: {\n              type: 'shadow'\n            },\n            axisLabel: {\n              rotate: 45,\n              fontSize: 10\n            }\n          }\n        ],\n        yAxis: [\n          {\n            type: 'value',\n            name: '营业额(万元)',\n            position: 'left',\n            axisLabel: {\n              formatter: function(value) {\n                if (value >= 10000) {\n                  return (value / 10000).toFixed(1) + '万';\n                }\n                return value;\n              }\n            }\n          },\n          {\n            type: 'value',\n            name: '营业额同比增长率',\n            position: 'right',\n            axisLabel: {\n              formatter: '{value}%'\n            }\n          }\n        ],\n        series: [\n          {\n            name: '营业额/万元',\n            type: 'bar',\n            data: this.storeRevenueData.revenue,\n            itemStyle: {\n              color: '#5B8FF9'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(1) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '利润/万元',\n            type: 'bar',\n            data: this.storeRevenueData.profit,\n            itemStyle: {\n              color: '#FFD666'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(1) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '营业额同比增长率',\n            type: 'line',\n            yAxisIndex: 1,\n            data: this.storeRevenueData.growthRate,\n            itemStyle: {\n              color: '#FF6B6B'\n            },\n            lineStyle: {\n              width: 2\n            },\n            label: {\n              show: true,\n              position: 'top',\n              formatter: '{c}%',\n              fontSize: 10\n            }\n          }\n        ]\n      };\n\n      this.storeRevenueChart.setOption(option);\n    },\n\n    /** 初始化云营业额图表 */\n    initCloudRevenueChart() {\n      this.cloudRevenueChart = echarts.init(this.$refs.cloudRevenueChart);\n\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'cross',\n            crossStyle: {\n              color: '#999'\n            }\n          }\n        },\n        legend: {\n          show: false\n        },\n        xAxis: [\n          {\n            type: 'category',\n            data: this.cloudRevenueData.categories,\n            axisPointer: {\n              type: 'shadow'\n            },\n            axisLabel: {\n              rotate: 45,\n              fontSize: 10\n            }\n          }\n        ],\n        yAxis: [\n          {\n            type: 'value',\n            name: '营业额(万元)',\n            position: 'left',\n            axisLabel: {\n              formatter: function(value) {\n                return (value / 10000).toFixed(0) + '万';\n              }\n            }\n          },\n          {\n            type: 'value',\n            name: '营业额同比增长率',\n            position: 'right',\n            axisLabel: {\n              formatter: '{value}%'\n            }\n          }\n        ],\n        series: [\n          {\n            name: '营业额/万元',\n            type: 'bar',\n            data: this.cloudRevenueData.revenue,\n            itemStyle: {\n              color: '#5B8FF9'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(0) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '利润/万元',\n            type: 'bar',\n            data: this.cloudRevenueData.profit,\n            itemStyle: {\n              color: '#FFD666'\n            },\n            barWidth: '20%',\n            label: {\n              show: true,\n              position: 'top',\n              formatter: function(params) {\n                return (params.value / 10000).toFixed(0) + '万';\n              },\n              fontSize: 10\n            }\n          },\n          {\n            name: '营业额同比增长率',\n            type: 'line',\n            yAxisIndex: 1,\n            data: this.cloudRevenueData.growthRate,\n            itemStyle: {\n              color: '#FF6B6B'\n            },\n            lineStyle: {\n              width: 2\n            },\n            label: {\n              show: true,\n              position: 'top',\n              formatter: '{c}%',\n              fontSize: 10\n            }\n          }\n        ]\n      };\n\n      this.cloudRevenueChart.setOption(option);\n    },\n\n    // 搜索栏图标按钮方法\n    handleClose() {\n      console.log('关闭搜索');\n      this.$message.info('关闭搜索');\n    },\n\n    handleSearch() {\n      console.log('执行搜索');\n      this.$message.success('搜索功能开发中...');\n    },\n\n    handleMinus() {\n      console.log('减号操作');\n      this.$message.info('减号功能开发中...');\n    },\n\n    handleFilter() {\n      console.log('筛选功能');\n      this.showFilterPopup = !this.showFilterPopup;\n\n      if (this.showFilterPopup) {\n        this.$nextTick(() => {\n          this.setFilterPopupPosition();\n        });\n      }\n    },\n\n    // 设置弹窗位置\n    setFilterPopupPosition() {\n      const filterButton = this.$refs.filterButton;\n      if (filterButton) {\n        const rect = filterButton.getBoundingClientRect();\n        this.filterPopupStyle = {\n          position: 'fixed',\n          top: rect.bottom + 5 + 'px',\n          right: (window.innerWidth - rect.right) + 'px',\n          zIndex: 1000\n        };\n      }\n    },\n\n    // 关闭筛选弹窗\n    closeFilterPopup() {\n      this.showFilterPopup = false;\n    },\n\n    // 选择筛选项\n    selectFilter(item) {\n      console.log('选择筛选项:', item);\n      this.$message.success(`已选择: ${item}`);\n      this.closeFilterPopup();\n    },\n\n    // 点击外部关闭弹窗\n    handleClickOutside(event) {\n      // 处理筛选弹窗\n      if (this.showFilterPopup) {\n        const filterButton = this.$refs.filterButton;\n        const popup = event.target.closest('.filter-popup');\n\n        // 如果点击的不是筛选按钮也不是弹窗内部，则关闭弹窗\n        if (!filterButton?.contains(event.target) && !popup) {\n          this.closeFilterPopup();\n        }\n      }\n\n      // 处理更多操作弹窗\n      if (this.showMorePopup) {\n        const moreButtons = [\n          this.$refs.moreButton1,\n          this.$refs.moreButton2,\n          this.$refs.moreButton3\n        ];\n        const morePopup = event.target.closest('.more-popup');\n\n        // 检查是否点击了任何更多按钮\n        const clickedMoreButton = moreButtons.some(button =>\n          button && button.contains(event.target)\n        );\n\n        // 如果点击的不是更多按钮也不是弹窗内部，则关闭弹窗\n        if (!clickedMoreButton && !morePopup) {\n          this.closeMorePopup();\n        }\n      }\n\n      // 处理分享弹窗\n      if (this.showSharePopup) {\n        const sharePopup = event.target.closest('.share-popup');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!sharePopup) {\n          this.closeSharePopup();\n        }\n      }\n\n      // 处理卡片提醒弹窗\n      if (this.showReminderPopup) {\n        const reminderPopup = event.target.closest('.reminder-popup');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!reminderPopup) {\n          this.closeReminderPopup();\n        }\n      }\n\n      // 处理上传CSV弹窗\n      if (this.showUploadPopup) {\n        const uploadPopup = event.target.closest('.upload-popup');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!uploadPopup) {\n          this.closeUploadPopup();\n        }\n      }\n\n      // 处理图表选择器弹窗\n      if (this.showChartSelector) {\n        const chartSelector = event.target.closest('.chart-selector');\n        // 如果点击的不是弹窗内部，则关闭弹窗\n        if (!chartSelector) {\n          this.closeChartSelector();\n        }\n      }\n\n      // 处理设置弹窗\n      if (this.showSettingsPopup) {\n        const settingsPopup = event.target.closest('.settings-popup');\n        const settingsButtons = document.querySelectorAll('.action-icon.settings');\n\n        // 检查是否点击了任何设置按钮\n        const clickedSettingsButton = Array.from(settingsButtons).some(button =>\n          button && button.contains(event.target)\n        );\n\n        // 如果点击的不是设置按钮也不是弹窗内部，则关闭弹窗\n        if (!clickedSettingsButton && !settingsPopup) {\n          this.closeSettingsPopup();\n        }\n      }\n    },\n\n    // 更多操作相关方法\n    handleMoreClick(event) {\n      this.showMorePopup = true;\n\n      this.$nextTick(() => {\n        this.setMorePopupPosition(event.target);\n      });\n    },\n\n    // 设置更多弹窗位置\n    setMorePopupPosition(buttonElement) {\n      if (buttonElement) {\n        const rect = buttonElement.getBoundingClientRect();\n        this.morePopupStyle = {\n          position: 'fixed',\n          top: rect.bottom + 5 + 'px',\n          left: rect.left - 60 + 'px', // 向左偏移一些，让弹窗居中对齐按钮\n          zIndex: 2000\n        };\n      }\n    },\n\n    closeMorePopup() {\n      this.showMorePopup = false;\n    },\n\n    handleCardReminder() {\n      this.showReminderPopup = true;\n      this.closeMorePopup();\n    },\n\n    handleShareCard() {\n      this.showSharePopup = true;\n      this.closeMorePopup();\n      this.$message.success('分享弹窗已打开');\n    },\n\n    handleSaveCard() {\n      console.log('保存卡片');\n      this.$message.success('保存卡片功能开发中...');\n      this.closeMorePopup();\n    },\n\n    handleUploadCSV() {\n      this.showUploadPopup = true;\n      this.closeMorePopup();\n    },\n\n    handleDownloadPNG() {\n      console.log('下载PNG');\n      this.$message.success('下载PNG功能开发中...');\n      this.closeMorePopup();\n    },\n\n    // 图表按钮处理方法\n    handleRefresh() {\n      console.log('刷新数据');\n      this.$message.success('正在刷新数据...');\n      // 重新初始化图表\n      this.initCharts();\n    },\n\n    handleDownload() {\n      console.log('下载数据');\n      this.$message.success('下载功能开发中...');\n    },\n\n    handleSettings(event) {\n      console.log('设置');\n      this.showSettingsPopup = true;\n\n      this.$nextTick(() => {\n        this.setSettingsPopupPosition(event.target);\n      });\n    },\n\n    // 分享弹窗相关方法\n    closeSharePopup() {\n      this.showSharePopup = false;\n    },\n\n    copyShareLink() {\n      // 复制链接到剪贴板\n      navigator.clipboard.writeText(this.shareLink).then(() => {\n        this.$message.success('链接已复制到剪贴板');\n      }).catch(() => {\n        // 降级方案\n        const textArea = document.createElement('textarea');\n        textArea.value = this.shareLink;\n        document.body.appendChild(textArea);\n        textArea.select();\n        document.execCommand('copy');\n        document.body.removeChild(textArea);\n        this.$message.success('链接已复制到剪贴板');\n      });\n    },\n\n    // 卡片提醒弹窗方法\n    closeReminderPopup() {\n      this.showReminderPopup = false;\n      // 重置表单\n      this.reminderForm = {\n        cardName: '',\n        email: '',\n        changeType: '同比增减幅',\n        timePeriod: '天数',\n        threshold: 0,\n        contentChange: false,\n        method: 'email'\n      };\n    },\n\n    confirmReminder() {\n      // 验证表单\n      if (!this.reminderForm.cardName) {\n        this.$message.warning('请选择提醒卡片');\n        return;\n      }\n      if (!this.reminderForm.email) {\n        this.$message.warning('请输入邮箱地址');\n        return;\n      }\n\n      // 这里可以添加邮箱格式验证\n      const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n      if (!emailRegex.test(this.reminderForm.email)) {\n        this.$message.warning('请输入正确的邮箱格式');\n        return;\n      }\n\n      console.log('设置卡片提醒:', this.reminderForm);\n      this.$message.success('卡片提醒设置成功！');\n      this.closeReminderPopup();\n    },\n\n    // 上传CSV弹窗方法\n    closeUploadPopup() {\n      this.showUploadPopup = false;\n      // 重置表单\n      this.uploadForm = {\n        reportName: '',\n        description: '',\n        file: null\n      };\n    },\n\n    handleFileSelect(event) {\n      const file = event.target.files[0];\n      if (file) {\n        // 检查文件类型\n        if (!file.name.toLowerCase().endsWith('.csv')) {\n          this.$message.warning('请选择CSV格式的文件');\n          event.target.value = '';\n          return;\n        }\n        this.uploadForm.file = file;\n      }\n    },\n\n    confirmUpload() {\n      // 验证表单\n      if (!this.uploadForm.reportName.trim()) {\n        this.$message.warning('请输入报告名称');\n        return;\n      }\n      if (!this.uploadForm.file) {\n        this.$message.warning('请选择要上传的CSV文件');\n        return;\n      }\n\n      console.log('上传CSV:', this.uploadForm);\n      this.$message.success('CSV文件上传成功！');\n      this.closeUploadPopup();\n    },\n\n    // 图表选择器相关方法\n    openChartSelector() {\n      this.showChartSelector = true;\n    },\n\n    closeChartSelector() {\n      this.showChartSelector = false;\n    },\n\n    selectChartType(chartType) {\n      this.selectedChartType = chartType;\n      console.log('选择图表类型:', chartType);\n      this.closeChartSelector();\n    },\n\n    // 设置弹窗相关方法\n    setSettingsPopupPosition(buttonElement) {\n      if (buttonElement) {\n        const rect = buttonElement.getBoundingClientRect();\n        this.settingsPopupStyle = {\n          position: 'fixed',\n          top: rect.bottom + 5 + 'px',\n          left: rect.left - 100 + 'px',\n          zIndex: 2000\n        };\n      }\n    },\n\n    closeSettingsPopup() {\n      this.showSettingsPopup = false;\n    },\n\n    selectChartIcon(chartType) {\n      console.log('选择图表类型:', chartType);\n      this.$message.success(`已选择图表类型: ${chartType}`);\n      this.closeSettingsPopup();\n    }\n  }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard-container {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: calc(100vh - 84px);\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n// 搜索栏样式\n.search-container {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 4px;\n  padding: 12px 16px;\n  margin-bottom: 16px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n\n  .search-form {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .search-input-wrapper {\n      flex: 1;\n\n      .search-input {\n        width: 100%;\n        height: 32px;\n        padding: 4px 12px;\n        border: 1px solid #d9d9d9;\n        border-radius: 4px;\n        font-size: 14px;\n        color: #333;\n        background: #fff;\n        outline: none;\n        transition: border-color 0.3s;\n\n        &:focus {\n          border-color: #1890ff;\n          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n        }\n\n        &::placeholder {\n          color: #999;\n        }\n      }\n    }\n\n    .search-buttons {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .btn-icon {\n        width: 32px;\n        height: 32px;\n        padding: 6px;\n        border: 1px solid #d9d9d9;\n        border-radius: 4px;\n        background: #fff;\n        cursor: pointer;\n        transition: all 0.3s;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n\n        &:hover {\n          background: #f5f5f5;\n          border-color: #40a9ff;\n        }\n\n        .close-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDRMNCA0TDQgMTJMMTIgMTJMMTIgNFoiIHN0cm9rZT0iIzY2NiIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K') no-repeat center;\n          background-size: contain;\n        }\n\n        .search-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTcgMTJDMy42ODYyOSAxMiAxIDkuMzEzNzEgMSA2QzEgMi42ODYyOSAzLjY4NjI5IDAgNyAwQzEwLjMxMzcgMCAxMyAyLjY4NjI5IDEzIDZDMTMgOS4zMTM3MSAxMC4zMTM3IDEyIDcgMTJaTTcgMTFDOS43NjE0MiAxMSAxMiA4Ljc2MTQyIDEyIDZDMTIgMy4yMzg1OCA5Ljc2MTQyIDEgNyAxQzQuMjM4NTggMSAyIDMuMjM4NTggMiA2QzIgOC43NjE0MiA0LjIzODU4IDExIDcgMTFaIiBmaWxsPSIjNjY2Ii8+CjxwYXRoIGQ9Ik0xMSAxMUwxNSAxNSIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=') no-repeat center;\n          background-size: contain;\n        }\n\n        .minus-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgOEgxMiIgc3Ryb2tlPSIjNjY2IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=') no-repeat center;\n          background-size: contain;\n        }\n\n        .filter-icon {\n          width: 16px;\n          height: 16px;\n          background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIgM0gxNEwxMCA3VjEzTDYgMTFWN0wyIDNaIiBzdHJva2U9IiM2NjYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==') no-repeat center;\n          background-size: contain;\n        }\n      }\n    }\n  }\n}\n\n// 筛选弹窗样式\n.filter-popup {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  width: 280px;\n  max-height: 450px;\n  overflow: hidden;\n\n  .popup-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 8px 12px;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafafa;\n    border-radius: 6px 6px 0 0;\n\n    span {\n      font-size: 14px;\n      font-weight: 500;\n      color: #262626;\n    }\n\n    .popup-close {\n      background: none;\n      border: none;\n      font-size: 16px;\n      color: #8c8c8c;\n      cursor: pointer;\n      padding: 0;\n      width: 20px;\n      height: 20px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border-radius: 2px;\n\n      &:hover {\n        background: #f5f5f5;\n        color: #595959;\n      }\n    }\n  }\n\n  .popup-search {\n    padding: 12px;\n    border-bottom: 1px solid #f0f0f0;\n    position: relative;\n\n    .search-input {\n      width: 100%;\n      padding: 6px 12px 6px 32px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 13px;\n      outline: none;\n      background: #ffffff;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);\n      }\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n    }\n\n    .search-icon {\n      position: absolute;\n      left: 20px;\n      top: 50%;\n      transform: translateY(-50%);\n      width: 14px;\n      height: 14px;\n      background: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"%23999\" stroke-width=\"2\"><circle cx=\"11\" cy=\"11\" r=\"8\"/><path d=\"m21 21-4.35-4.35\"/></svg>') no-repeat center;\n      background-size: contain;\n    }\n  }\n\n  .popup-tabs {\n    display: flex;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafafa;\n\n    .tab-item {\n      flex: 1;\n      padding: 8px 12px;\n      text-align: center;\n      font-size: 13px;\n      color: #595959;\n      cursor: pointer;\n      transition: all 0.2s;\n      border-bottom: 2px solid transparent;\n\n      &:hover {\n        color: #1890ff;\n        background: #f5f5f5;\n      }\n\n      &.active {\n        color: #1890ff;\n        background: #ffffff;\n        border-bottom-color: #1890ff;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .popup-content {\n    padding: 0;\n    max-height: 300px;\n    overflow-y: auto;\n\n    .tab-content {\n      .filter-item {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 10px 16px;\n        font-size: 14px;\n        color: #262626;\n        cursor: pointer;\n        transition: all 0.2s;\n        border-bottom: 1px solid #f5f5f5;\n        line-height: 1.4;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        &:hover {\n          background: #f0f8ff;\n          color: #1890ff;\n        }\n\n        &:active {\n          background: #e6f7ff;\n          color: #1890ff;\n        }\n\n        .arrow-icon {\n          font-size: 12px;\n          color: #8c8c8c;\n          font-style: normal;\n        }\n      }\n\n      .time-units-row {\n        padding: 10px 16px;\n        border-bottom: 1px solid #f5f5f5;\n        display: flex;\n        gap: 16px;\n\n        .time-unit {\n          font-size: 14px;\n          color: #262626;\n          cursor: pointer;\n          transition: all 0.2s;\n          padding: 4px 8px;\n          border-radius: 4px;\n\n          &:hover {\n            background: #f0f8ff;\n            color: #1890ff;\n          }\n        }\n      }\n\n      .time-item {\n        padding: 10px 16px;\n        font-size: 14px;\n        color: #262626;\n        cursor: pointer;\n        transition: all 0.2s;\n        border-bottom: 1px solid #f5f5f5;\n        line-height: 1.4;\n\n        &:last-child {\n          border-bottom: none;\n        }\n\n        &:hover {\n          background: #f0f8ff;\n          color: #1890ff;\n        }\n      }\n    }\n  }\n}\n\n.search-form {\n  .form-row {\n    display: flex;\n    align-items: center;\n    gap: 16px;\n    flex-wrap: wrap;\n  }\n\n  .form-group {\n    display: flex;\n    align-items: center;\n    gap: 6px;\n\n    .form-label {\n      font-size: 13px;\n      color: #595959;\n      font-weight: 400;\n      white-space: nowrap;\n      margin: 0;\n    }\n\n    .form-input {\n      height: 28px;\n      padding: 0 8px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 13px;\n      color: #262626;\n      background: #ffffff;\n      outline: none;\n      transition: border-color 0.2s;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);\n      }\n\n      &.keyword-input {\n        width: 200px;\n      }\n\n      &.time-input {\n        width: 60px;\n      }\n\n      &::placeholder {\n        color: #bfbfbf;\n        font-size: 13px;\n      }\n    }\n\n    .form-select {\n      height: 28px;\n      padding: 0 20px 0 8px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 13px;\n      color: #262626;\n      background: #ffffff url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e\") no-repeat right 6px center/12px 12px;\n      outline: none;\n      appearance: none;\n      cursor: pointer;\n      min-width: 80px;\n      transition: border-color 0.2s;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);\n      }\n    }\n  }\n\n  .form-buttons {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    margin-left: auto;\n\n    .btn {\n      height: 28px;\n      padding: 0 12px;\n      border-radius: 4px;\n      font-size: 13px;\n      font-weight: 400;\n      border: 1px solid;\n      cursor: pointer;\n      outline: none;\n      transition: all 0.2s;\n      white-space: nowrap;\n\n      &.btn-primary {\n        background: #1890ff;\n        border-color: #1890ff;\n        color: #ffffff;\n\n        &:hover {\n          background: #40a9ff;\n          border-color: #40a9ff;\n        }\n      }\n\n      &.btn-default {\n        background: #ffffff;\n        border-color: #d9d9d9;\n        color: #595959;\n\n        &:hover {\n          color: #1890ff;\n          border-color: #1890ff;\n        }\n      }\n\n      &.btn-success {\n        background: #52c41a;\n        border-color: #52c41a;\n        color: #ffffff;\n\n        &:hover {\n          background: #73d13d;\n          border-color: #73d13d;\n        }\n      }\n    }\n  }\n}\n\n// 顶部区域：门店营业额前十的 + 智能助手\n.top-section {\n  display: flex;\n  gap: 20px;\n  align-items: flex-start;\n}\n\n// 中间区域：营业额同比 单独一行\n.middle-section {\n  width: 100%;\n}\n\n// 底部区域：品牌门店营业额前十的 + 智能助手\n.bottom-section {\n  display: flex;\n  gap: 20px;\n  align-items: flex-start;\n}\n\n// 主图表区域\n.main-chart {\n  flex: 1;\n  min-width: 0;\n}\n\n// 智能助手面板\n.assistant-panel {\n  width: 280px;\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  flex-shrink: 0;\n  display: flex;\n  flex-direction: column;\n  height: 500px;\n\n  .panel-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 16px 20px;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafbfc;\n    flex-shrink: 0;\n\n    span {\n      font-size: 14px;\n      font-weight: 500;\n      color: #262626;\n    }\n\n    .header-actions {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .send-btn {\n        width: 28px;\n        height: 28px;\n        border-radius: 50%;\n        background: #1890ff;\n        border: none;\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        transition: all 0.2s;\n\n        &:hover {\n          background: #40a9ff;\n          transform: scale(1.05);\n        }\n\n        &:active {\n          background: #096dd9;\n          transform: scale(0.95);\n        }\n      }\n\n      .panel-close {\n        cursor: pointer;\n        font-size: 16px;\n        color: #8c8c8c;\n\n        &:hover {\n          color: #262626;\n        }\n      }\n    }\n  }\n\n  .panel-content {\n    display: flex;\n    flex-direction: column;\n    flex: 1;\n    overflow: hidden;\n\n    .chat-messages {\n      flex: 1;\n      padding: 16px;\n      overflow-y: auto;\n      display: flex;\n      flex-direction: column;\n      gap: 12px;\n\n      .message-item {\n        display: flex;\n        align-items: flex-start;\n        gap: 8px;\n\n        &.user-message {\n          flex-direction: row-reverse;\n\n          .message-content {\n            background: #1890ff;\n            color: white;\n            border-radius: 12px 12px 4px 12px;\n            max-width: 70%;\n          }\n        }\n\n        &.assistant-message {\n          .message-avatar {\n            width: 32px;\n            height: 32px;\n            border-radius: 50%;\n            background: #f0f8ff;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            flex-shrink: 0;\n\n            .avatar-circle {\n              width: 24px;\n              height: 24px;\n              border-radius: 50%;\n              background: #e6f7ff;\n              display: flex;\n              align-items: center;\n              justify-content: center;\n            }\n          }\n\n          .message-content {\n            background: #f5f5f5;\n            color: #262626;\n            border-radius: 12px 12px 12px 4px;\n            max-width: 70%;\n          }\n        }\n\n        .message-content {\n          padding: 8px 12px;\n\n          .message-text {\n            font-size: 13px;\n            line-height: 1.4;\n            margin-bottom: 4px;\n          }\n\n          .message-time {\n            font-size: 11px;\n            opacity: 0.7;\n          }\n        }\n      }\n\n      .suggestion-item {\n        display: flex;\n        align-items: center;\n        padding: 8px 12px;\n        margin: 4px 0;\n        background: #f8f9fa;\n        border-radius: 6px;\n        cursor: pointer;\n        transition: background-color 0.2s;\n\n        &:hover {\n          background: #e9ecef;\n        }\n\n        .suggestion-icon {\n          margin-right: 8px;\n          font-size: 14px;\n        }\n\n        .suggestion-text {\n          font-size: 13px;\n          color: #666;\n        }\n      }\n    }\n\n    .input-area {\n      padding: 12px 16px;\n      border-top: 1px solid #f0f0f0;\n      background: #fafbfc;\n      flex-shrink: 0;\n\n      .input-wrapper {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        background: white;\n        border: 1px solid #d9d9d9;\n        border-radius: 20px;\n        padding: 6px 12px;\n\n        &:focus-within {\n          border-color: #1890ff;\n          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n        }\n\n        .chat-input {\n          flex: 1;\n          border: none;\n          outline: none;\n          font-size: 13px;\n          padding: 4px 0;\n          background: transparent;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n        }\n\n        .input-send-btn {\n          width: 24px;\n          height: 24px;\n          border: none;\n          background: transparent;\n          cursor: pointer;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          border-radius: 50%;\n          transition: all 0.2s;\n\n          &:hover {\n            background: #f0f8ff;\n          }\n\n          &:active {\n            background: #e6f7ff;\n          }\n        }\n      }\n    }\n  }\n}\n\n.chart-card, .value-card {\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.chart-header, .value-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafbfc;\n}\n\n.chart-title, .value-title {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 14px;\n  font-weight: 500;\n  color: #262626;\n\n  .chart-icon {\n    width: 16px;\n    height: 16px;\n    background: #1890ff;\n    border-radius: 2px;\n  }\n\n  .help-icon {\n    width: 16px;\n    height: 16px;\n    background: #d9d9d9;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 12px;\n    color: #ffffff;\n    cursor: pointer;\n  }\n}\n\n.chart-meta, .value-meta {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 12px;\n  color: #8c8c8c;\n\n  .chart-date, .value-date {\n    color: #595959;\n  }\n\n  .chart-type, .value-type {\n    background: #f0f0f0;\n    padding: 2px 6px;\n    border-radius: 2px;\n  }\n\n  .chart-source {\n    color: #1890ff;\n  }\n}\n\n.chart-actions, .value-actions {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n\n  .action-icon {\n    width: 16px;\n    height: 16px;\n    border-radius: 2px;\n    cursor: pointer;\n    background-size: 12px 12px;\n    background-position: center;\n    background-repeat: no-repeat;\n    transition: all 0.2s;\n\n    &.refresh {\n      background-color: #52c41a;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #73d13d;\n      }\n    }\n\n    &.download {\n      background-color: #1890ff;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #40a9ff;\n      }\n    }\n\n    &.more {\n      background-color: #8c8c8c;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #a6a6a6;\n      }\n    }\n\n    &.settings {\n      background-color: #722ed1;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3crect x='3' y='12' width='4' height='9'/%3e%3crect x='10' y='8' width='4' height='13'/%3e%3crect x='17' y='4' width='4' height='17'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #9254de;\n      }\n    }\n\n    &.close {\n      background-color: #ff4d4f;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white' stroke-width='2'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='M6 18L18 6M6 6l12 12'/%3e%3c/svg%3e\");\n\n      &:hover {\n        background-color: #ff7875;\n      }\n    }\n  }\n\n  .chart-status {\n    font-size: 12px;\n    color: #8c8c8c;\n    margin-left: 8px;\n  }\n}\n\n.chart-content {\n  padding: 20px;\n}\n\n.chart-legend {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  margin-bottom: 16px;\n  font-size: 12px;\n\n  .legend-item {\n    display: flex;\n    align-items: center;\n    gap: 6px;\n\n    .legend-color {\n      width: 12px;\n      height: 12px;\n      border-radius: 2px;\n\n      &.blue {\n        background: #5B8FF9;\n      }\n\n      &.yellow {\n        background: #FFD666;\n      }\n\n      &.line {\n        background: #FF6B6B;\n        border-radius: 50%;\n        width: 8px;\n        height: 8px;\n      }\n    }\n  }\n}\n\n.chart-wrapper {\n  width: 100%;\n  height: 300px;\n}\n\n.chart {\n  width: 100%;\n  height: 100%;\n}\n\n.value-content {\n  padding: 20px;\n}\n\n.value-main {\n  .value-label {\n    font-size: 12px;\n    color: #8c8c8c;\n    margin-bottom: 8px;\n    display: block;\n  }\n\n  .value-number {\n    font-size: 36px;\n    font-weight: bold;\n    color: #262626;\n    line-height: 1;\n    margin-bottom: 12px;\n\n    .value-unit {\n      font-size: 18px;\n      color: #8c8c8c;\n      margin-left: 4px;\n    }\n  }\n\n  .value-change {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    font-size: 12px;\n\n    .change-text {\n      color: #8c8c8c;\n    }\n\n    .change-value {\n      &.positive {\n        color: #52c41a;\n      }\n\n      &.negative {\n        color: #ff4d4f;\n      }\n    }\n\n    .change-arrow {\n      width: 0;\n      height: 0;\n\n      &.up {\n        border-left: 4px solid transparent;\n        border-right: 4px solid transparent;\n        border-bottom: 6px solid #52c41a;\n      }\n\n      &.down {\n        border-left: 4px solid transparent;\n        border-right: 4px solid transparent;\n        border-top: 6px solid #ff4d4f;\n      }\n    }\n  }\n}\n\n.control-panel {\n  position: fixed;\n  right: 20px;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 280px;\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n  z-index: 1000;\n\n  .panel-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 16px 20px;\n    border-bottom: 1px solid #f0f0f0;\n    background: #fafbfc;\n    border-radius: 8px 8px 0 0;\n\n    span {\n      font-size: 14px;\n      font-weight: 500;\n      color: #262626;\n    }\n\n    .panel-close {\n      cursor: pointer;\n      font-size: 16px;\n      color: #8c8c8c;\n    }\n  }\n\n  .panel-content {\n    padding: 20px;\n\n    .panel-section {\n      h4 {\n        margin: 0 0 12px 0;\n        font-size: 14px;\n        color: #262626;\n      }\n\n      .setting-item {\n        margin-bottom: 16px;\n\n        label {\n          display: block;\n          margin-bottom: 6px;\n          font-size: 12px;\n          color: #8c8c8c;\n        }\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 1200px) {\n  .search-container {\n    .search-form {\n      .form-row {\n        gap: 12px;\n      }\n\n      .form-group {\n        .form-input.keyword-input {\n          width: 160px;\n        }\n      }\n\n      .form-buttons {\n        margin-left: 0;\n        margin-top: 8px;\n        width: 100%;\n        justify-content: flex-start;\n      }\n    }\n  }\n\n  .top-section, .bottom-section {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .assistant-panel {\n    width: 100%;\n    order: -1; // 智能助手面板在移动端显示在图表上方\n  }\n}\n\n@media (max-width: 768px) {\n  .dashboard-container {\n    padding: 10px;\n    gap: 15px;\n  }\n\n  .search-container {\n    .search-form {\n      .form-row {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 12px;\n      }\n\n      .form-group {\n        width: 100%;\n\n        .form-input {\n          flex: 1;\n          min-width: 120px;\n\n          &.keyword-input {\n            width: 100%;\n          }\n        }\n\n        .form-select {\n          flex: 1;\n          min-width: 120px;\n        }\n      }\n\n      .form-buttons {\n        width: 100%;\n        justify-content: center;\n        margin-top: 12px;\n      }\n    }\n  }\n\n  .top-section, .bottom-section {\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .assistant-panel {\n    width: 100%;\n    order: -1;\n\n    .panel-content {\n      padding: 15px;\n\n      .assistant-item {\n        padding: 10px 0;\n\n        .assistant-icon {\n          width: 28px;\n          height: 28px;\n          font-size: 16px;\n        }\n\n        .assistant-text {\n          font-size: 13px;\n        }\n      }\n    }\n  }\n\n  .chart-header, .value-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n    padding: 12px 16px;\n  }\n\n  .chart-meta, .value-meta {\n    order: 1;\n  }\n\n  .chart-actions, .value-actions {\n    order: 2;\n    align-self: flex-end;\n  }\n\n  .chart-content {\n    padding: 16px;\n  }\n\n  .value-content {\n    padding: 16px;\n  }\n\n  .chart-wrapper {\n    height: 250px;\n  }\n\n  .value-number {\n    font-size: 28px !important;\n\n    .value-unit {\n      font-size: 14px !important;\n    }\n  }\n}\n\n// 更多操作弹窗样式\n.more-popup {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  min-width: 160px;\n  overflow: hidden;\n  position: fixed;\n  z-index: 2000;\n\n  .more-popup-content {\n    .more-action-item {\n      padding: 12px 16px;\n      font-size: 14px;\n      color: #262626;\n      cursor: pointer;\n      transition: all 0.2s;\n      border-bottom: 1px solid #f5f5f5;\n      line-height: 1.4;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      &:hover {\n        background: #f0f8ff;\n        color: #1890ff;\n      }\n\n      &:active {\n        background: #e6f7ff;\n        color: #1890ff;\n      }\n    }\n  }\n}\n\n// 分享弹窗样式\n.share-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.share-popup {\n  background: white;\n  border-radius: 8px;\n  width: 400px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n}\n\n.share-popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.share-popup-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.share-popup-close {\n  background: none;\n  border: none;\n  font-size: 20px;\n  color: #999;\n  cursor: pointer;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: #666;\n  }\n}\n\n.share-popup-content {\n  padding: 20px;\n}\n\n.share-description {\n  color: #666;\n  font-size: 14px;\n  margin-bottom: 20px;\n  line-height: 1.5;\n}\n\n.share-option {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.share-option-label {\n  font-size: 14px;\n  color: #333;\n}\n\n.share-toggle {\n  position: relative;\n}\n\n.toggle-input {\n  display: none;\n}\n\n.toggle-label {\n  display: block;\n  width: 44px;\n  height: 24px;\n  background: #ddd;\n  border-radius: 12px;\n  cursor: pointer;\n  position: relative;\n  transition: background 0.3s;\n\n  &::after {\n    content: '';\n    position: absolute;\n    top: 2px;\n    left: 2px;\n    width: 20px;\n    height: 20px;\n    background: white;\n    border-radius: 50%;\n    transition: transform 0.3s;\n  }\n}\n\n.toggle-input:checked + .toggle-label {\n  background: #1890ff;\n\n  &::after {\n    transform: translateX(20px);\n  }\n}\n\n.share-link-section {\n  display: flex;\n  gap: 8px;\n}\n\n.share-link-input {\n  flex: 1;\n  height: 36px;\n  padding: 0 12px;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  font-size: 14px;\n  color: #666;\n  background: #f5f5f5;\n  outline: none;\n}\n\n.copy-link-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: #1890ff;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  font-size: 14px;\n  cursor: pointer;\n  transition: background 0.3s;\n\n  &:hover {\n    background: #40a9ff;\n  }\n\n  &:active {\n    background: #096dd9;\n  }\n}\n\n// 卡片提醒弹窗样式\n.reminder-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 10000 !important;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.reminder-popup {\n  background: white;\n  border-radius: 8px;\n  width: 520px;\n  max-width: 90vw;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n  z-index: 10001 !important;\n  position: relative;\n}\n\n.reminder-popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.reminder-popup-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.reminder-popup-close {\n  background: none;\n  border: none;\n  font-size: 20px;\n  color: #999;\n  cursor: pointer;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: #666;\n  }\n}\n\n.reminder-popup-content {\n  padding: 20px;\n}\n\n.reminder-form-item {\n  margin-bottom: 16px;\n\n  .reminder-label {\n    display: block;\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n\n  .reminder-select {\n    width: 100%;\n    height: 36px;\n    padding: 0 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n    cursor: pointer;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n  }\n\n  .reminder-input {\n    width: 100%;\n    height: 36px;\n    padding: 0 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n  }\n\n  .reminder-change-section {\n    display: flex;\n    gap: 12px;\n\n    .reminder-select-small {\n      flex: 1;\n      height: 36px;\n      padding: 0 12px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 14px;\n      color: #333;\n      background: white;\n      outline: none;\n      cursor: pointer;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n      }\n    }\n  }\n\n  .reminder-threshold-section {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .reminder-number-input {\n      width: 120px;\n      height: 36px;\n      padding: 0 12px;\n      border: 1px solid #d9d9d9;\n      border-radius: 4px;\n      font-size: 14px;\n      color: #333;\n      background: white;\n      outline: none;\n\n      &:focus {\n        border-color: #1890ff;\n        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n      }\n    }\n\n    .reminder-unit {\n      font-size: 14px;\n      color: #666;\n    }\n\n    .reminder-checkbox-section {\n      display: flex;\n      align-items: center;\n      gap: 6px;\n      margin-left: auto;\n\n      .reminder-checkbox {\n        width: 16px;\n        height: 16px;\n        cursor: pointer;\n      }\n\n      .reminder-checkbox-label {\n        font-size: 14px;\n        color: #333;\n        cursor: pointer;\n        margin: 0;\n      }\n    }\n  }\n\n  .reminder-method-section {\n    display: flex;\n    gap: 20px;\n\n    .reminder-radio-item {\n      display: flex;\n      align-items: center;\n      gap: 6px;\n\n      .reminder-radio {\n        width: 16px;\n        height: 16px;\n        cursor: pointer;\n      }\n\n      .reminder-radio-label {\n        font-size: 14px;\n        color: #333;\n        cursor: pointer;\n        margin: 0;\n      }\n    }\n  }\n}\n\n.reminder-description {\n  font-size: 13px;\n  color: #999;\n  margin: 16px 0;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  line-height: 1.5;\n}\n\n.reminder-popup-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  padding: 16px 20px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.reminder-cancel-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: white;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  color: #666;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    color: #1890ff;\n    border-color: #1890ff;\n  }\n}\n\n.reminder-confirm-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: #1890ff;\n  border: none;\n  border-radius: 4px;\n  color: white;\n  font-size: 14px;\n  cursor: pointer;\n  transition: background 0.3s;\n\n  &:hover {\n    background: #40a9ff;\n  }\n\n  &:active {\n    background: #096dd9;\n  }\n}\n\n// 上传CSV弹窗样式\n.upload-popup-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.upload-popup {\n  background: white;\n  border-radius: 8px;\n  width: 480px;\n  max-width: 90vw;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  overflow: hidden;\n}\n\n.upload-popup-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px 20px;\n  border-bottom: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.upload-popup-title {\n  font-size: 16px;\n  font-weight: 500;\n  color: #333;\n}\n\n.upload-popup-close {\n  background: none;\n  border: none;\n  font-size: 20px;\n  color: #999;\n  cursor: pointer;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n\n  &:hover {\n    color: #666;\n  }\n}\n\n.upload-popup-content {\n  padding: 20px;\n}\n\n.upload-form-item {\n  margin-bottom: 16px;\n\n  .upload-label {\n    display: block;\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n  }\n\n  .upload-input {\n    width: 100%;\n    height: 36px;\n    padding: 0 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n  }\n\n  .upload-textarea {\n    width: 100%;\n    padding: 8px 12px;\n    border: 1px solid #d9d9d9;\n    border-radius: 4px;\n    font-size: 14px;\n    color: #333;\n    background: white;\n    outline: none;\n    resize: vertical;\n    min-height: 80px;\n\n    &:focus {\n      border-color: #1890ff;\n      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n    }\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n  }\n\n  .upload-file-section {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .upload-file-input {\n      display: none;\n    }\n\n    .upload-file-button {\n      height: 36px;\n      padding: 0 16px;\n      background: #1890ff;\n      border: none;\n      border-radius: 4px;\n      color: white;\n      font-size: 14px;\n      cursor: pointer;\n      transition: background 0.3s;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      &:hover {\n        background: #40a9ff;\n      }\n\n      &:active {\n        background: #096dd9;\n      }\n    }\n\n    .upload-file-name {\n      font-size: 14px;\n      color: #333;\n      flex: 1;\n    }\n\n    .upload-file-placeholder {\n      font-size: 14px;\n      color: #bfbfbf;\n      flex: 1;\n    }\n  }\n}\n\n.upload-tips {\n  margin-top: 20px;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  border-left: 3px solid #1890ff;\n\n  .upload-tips-title {\n    font-size: 14px;\n    font-weight: 500;\n    color: #333;\n    margin-bottom: 8px;\n  }\n\n  .upload-tips-content {\n    font-size: 13px;\n    color: #666;\n    line-height: 1.6;\n  }\n}\n\n.upload-popup-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  padding: 16px 20px;\n  border-top: 1px solid #f0f0f0;\n  background: #fafafa;\n}\n\n.upload-cancel-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: white;\n  border: 1px solid #d9d9d9;\n  border-radius: 4px;\n  color: #666;\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s;\n\n  &:hover {\n    color: #1890ff;\n    border-color: #1890ff;\n  }\n}\n\n.upload-confirm-btn {\n  height: 36px;\n  padding: 0 16px;\n  background: #1890ff;\n  border: none;\n  border-radius: 4px;\n  color: white;\n  font-size: 14px;\n  cursor: pointer;\n  transition: background 0.3s;\n\n  &:hover {\n    background: #40a9ff;\n  }\n\n  &:active {\n    background: #096dd9;\n  }\n}\n\n// 设置弹窗样式\n.settings-popup {\n  background: #ffffff;\n  border: 1px solid #e8e8e8;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  padding: 8px;\n  overflow: hidden;\n\n  .settings-popup-content {\n    .chart-types-grid {\n      display: grid;\n      grid-template-columns: repeat(4, 1fr);\n      gap: 8px;\n\n      .chart-type-item {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        padding: 8px 4px;\n        cursor: pointer;\n        transition: all 0.2s;\n        border-radius: 4px;\n        min-height: 60px;\n\n        &:hover {\n          background: #f0f8ff;\n          transform: translateY(-2px);\n        }\n\n        &:active {\n          background: #e6f7ff;\n          transform: translateY(0);\n        }\n\n        .chart-icon {\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          margin-bottom: 4px;\n\n          svg {\n            width: 20px;\n            height: 20px;\n            transition: all 0.2s;\n          }\n        }\n\n        .chart-label {\n          font-size: 12px;\n          color: #262626;\n          text-align: center;\n          line-height: 1.2;\n          white-space: nowrap;\n        }\n\n        &:hover {\n          .chart-icon svg {\n            transform: scale(1.1);\n          }\n\n          .chart-label {\n            color: #1890ff;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}